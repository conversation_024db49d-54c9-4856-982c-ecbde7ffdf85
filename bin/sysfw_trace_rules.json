{"description_trace": {"action_id": {"higher_bit": 28, "lower_bit": 22}, "sub_action_id": {"higher_bit": 21, "lower_bit": 16}, "detect_regex": "^0x[0-9A-Za-z]+$", "domain": {"higher_bit": 31, "lower_bit": 29}, "message_data": {"higher_bit": 21, "lower_bit": 0}, "sub_action_message_data": {"higher_bit": 15, "lower_bit": 0}, "trace_data_version_action": {"domain": "0x00", "action": "0x10"}}, "message_decode": {"0x00": {"action_domain_name": "BasePort", "actions": {"0x00": {"action_long": "OSAL/Baseport init complete", "action_short": "INIT_COMPLETE"}, "0x10": {"action_long": "OSAL/Baseport trace data version", "action_short": "TRACE_DATA_VERSION", "msg_data": [{"fmt": "0x%02x", "higher_bit": 19, "lower_bit": 12, "name": "Trace version major"}, {"fmt": "0x%03x", "higher_bit": 11, "lower_bit": 0, "name": "Trace version minor"}]}, "0x11": {"action_long": "System Firmware version", "action_short": "SYSFW_VERSION", "msg_data": [{"fmt": "%d", "higher_bit": 15, "lower_bit": 8, "name": "version"}, {"fmt": "%d", "higher_bit": 7, "lower_bit": 4, "name": "subversion"}, {"fmt": "%d", "higher_bit": 3, "lower_bit": 0, "name": "patch"}]}, "0x01": {"action_long": "TISCI Message interrupt handled", "action_short": "TISCI_MSG_RECEIVED", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 16, "name": "Queue ID"}, {"fmt": "%x", "higher_bit": 15, "lower_bit": 0, "name": "Message ID"}]}, "0x02": {"action_long": "Message from secure host received", "action_short": "TISCI_MSG_SENDER_HOST_ID", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 16, "name": "Queue ID"}, {"fmt": "%d", "higher_bit": 15, "lower_bit": 0, "name": "Host ID"}]}, "0x0A": {"action_long": "OSAL Context Switch", "action_short": "CONTEXT_SWITCH", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 0, "name": "Direction"}]}, "0x7F": {"action_long": "Generic Debug Message", "action_short": "GENERIC_DEBUG", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 0, "name": "Message Data"}]}}}, "0x01": {"action_domain_name": "Security", "action_modifiers": [{"mask": "0x40", "modifier_long": "Action failed", "modifier_short": "FAIL"}], "actions": {"0x00": {"action_long": "Firewall is activated", "action_short": "FIREWALL_ACTIVE", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 0, "name": "Firewall ID"}]}, "0x01": {"action_long": "RESERVED", "action_short": "RESERVED"}, "0x02": {"action_long": "Security post boardconfig initialization", "action_short": "SEC_INIT", "msg_data": [{"fmt": "%d", "higher_bit": 0, "lower_bit": 0, "name": "Start(0) or End(1)"}]}, "0x03": {"action_long": "Points of failures during secure boot api call", "action_short": "SEC_BOOT", "msg_data": [{"fmt": "%d", "higher_bit": 1, "lower_bit": 0, "name": "Error code"}]}, "0x04": {"action_long": "Points of failures during run time OTP Revision read/write", "action_short": "OTP_REV", "msg_data": [{"fmt": "%d", "higher_bit": 2, "lower_bit": 0, "name": "Error code"}]}, "0x7F": {"action_long": "Generic Debug Message", "action_short": "GENERIC_DEBUG", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 0, "name": "Message Data"}]}}}, "0x02": {"action_domain_name": "Resource Management", "action_modifiers": [{"mask": "0x40", "modifier_long": "Action failed", "modifier_short": "FAIL"}], "actions": {"0x00": {"versions": {"0x00000": {"action_long": "NavSS ring has been allocated", "action_short": "RING_ALLOCATE (LEGACY-UNSUPPORTED)", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "NavSS Device ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "Ring index"}]}, "0x01000": {"action_long": "RM init", "action_short": "RM_INIT"}}}, "0x01": {"versions": {"0x00000": {"action_long": "NavSS ring has been freed", "action_short": "RING_FREE (LEGACY-UNSUPPORTED)", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "NavSS Device ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "Ring index"}]}, "0x01000": {"action_long": "RM core init", "action_short": "RM_CORE_INIT"}}}, "0x02": {"action_long": "NavSS ring has been reconfigured", "action_short": "RING_RECONFIG (LEGACY-UNSUPPORTED)", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "NavSS Device ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "Ring index"}]}, "0x03": {"versions": {"0x00000": {"action_long": "NavSS ring has been reset", "action_short": "RING_RESET (LEGACY-UNSUPPORTED)", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "NavSS Device ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "Ring index"}]}, "0x01000": {"action_long": "RA driver init", "action_short": "RA_INIT"}}}, "0x04": {"versions": {"0x00000": {"action_long": "NavSS ring has been configured", "action_short": "RING_CONFIGURE", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "NavSS Device ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "Ring index"}]}, "0x01000": {"action_long": "Configure NavSS ring", "action_short": "RING_CONFIGURE", "sub_actions": {"0x00": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "RA device ID"}]}, "0x01": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "index"}]}, "0x07": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "upper 16-bits of valid_params"}]}, "0x08": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "lower 16-bits of valid_params"}]}, "0x0A": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "virtid"}]}, "0x0B": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "mode"}]}, "0x0C": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "size"}]}, "0x0D": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "upper 16-bits of addr_lo"}]}, "0x0E": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "lower 16-bits of addr_lo"}]}, "0x0F": {"msg_data": [{"fmt": "0x%1X", "higher_bit": 16, "lower_bit": 0, "name": "upper 4-bits of count"}]}, "0x10": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "lower 16-bits of count"}]}, "0x11": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "order_id"}]}}}}}, "0x05": {"versions": {"0x00000": {"action_long": "NavSS ring get configuration", "action_short": "RING_GET_CFG", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "NavSS Device ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "Ring index"}]}, "0x01000": {"action_long": "Get NavSS ring configuration", "action_short": "RING_GET_CFG", "sub_actions": {"0x00": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "RA device ID"}]}, "0x01": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "index"}]}, "0x02": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "get_reset_cfg"}]}}}}}, "0x06": {"versions": {"0x01000": {"action_long": "NavSS ring index validation", "action_short": "RING_VALIDATE_INDEX", "sub_actions": {"0x01": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "index"}]}, "0x05": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "NavSS subsystem device ID"}]}}}}}, "0x07": {"versions": {"0x01000": {"action_long": "Configure NavSS ring monitor", "action_short": "RING_MON_CFG", "sub_actions": {"0x00": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "RA device ID"}]}, "0x01": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "monitor index"}]}, "0x07": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "upper 16-bits of valid_params"}]}, "0x08": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "lower 16-bits of valid_params"}]}, "0x12": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "source"}]}, "0x13": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "mode"}]}, "0x14": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "queue, or ring, to monitor"}]}}}}}, "0x08": {"versions": {"0x00000": {"action_long": "NavSS ring OES event has been programmed", "action_short": "RING_OES_INDEX", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "NavSS Device ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "OES register index"}]}, "0x01000": {"action_long": "Set OES register in NavSS ring accelerator", "action_short": "RING_OES_SET", "sub_actions": {"0x00": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "RA device ID"}]}, "0x01": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "index"}]}, "0x03": {"msg_data": [{"fmt": "0x%04x", "higher_bit": 16, "lower_bit": 0, "name": "global event"}]}, "0x04": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "OES register index"}]}}}}}, "0x09": {"versions": {"0x00000": {"action_long": "NavSS ring OES event has been programmed", "action_short": "RING_OES_EVENT", "msg_data": [{"fmt": "%d", "higher_bit": 15, "lower_bit": 0, "name": "Global event programmed into OES register"}]}, "0x01000": {"action_long": "Get NavSS ring OES event configuration", "action_short": "RING_OES_GET", "sub_actions": {"0x00": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "RA device ID"}]}, "0x01": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "index"}]}, "0x03": {"msg_data": [{"fmt": "0x%04x", "higher_bit": 16, "lower_bit": 0, "name": "global event"}]}, "0x04": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "OES register index"}]}}}}}, "0x0A": {"action_long": "NavSS UDMAP TX channel has been allocated", "action_short": "UDMAP_TX_CH_ALLOC (LEGACY-UNSUPPORTED)", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "NavSS Device ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "TX channel index"}]}, "0x0B": {"action_long": "NavSS UDMAP TX channel has been freed", "action_short": "UDMAP_TX_CH_FREE (LEGACY-UNSUPPORTED)", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "NavSS Device ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "TX channel index"}]}, "0x0C": {"versions": {"0x00000": {"action_long": "NavSS UDMAP TX channel has been modified", "action_short": "UDMAP_TX_CH_INDEX", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "NavSS Device ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "TX channel index"}]}, "0x01000": {"action_long": "UDMA driver init", "action_short": "UDMAP_INIT"}}}, "0x0D": {"versions": {"0x00000": {"action_long": "NavSS UDMAP TX channel thread ID modified", "action_short": "UDMAP_TX_CH_THRD_ID", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 0, "name": "PSI-L thread ID programmed into the TX channel THRD_ID register"}]}, "0x01000": {"action_long": "Set NavSS UDMAP TX channel thread ID", "action_short": "UDMAP_TX_CH_SET_THRD_ID", "sub_actions": {"0x01": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "index"}]}, "0x05": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "NavSS subsystem device ID"}]}, "0x1E": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "PSI-L thread ID"}]}}}}}, "0x0E": {"versions": {"0x00000": {"action_long": "NavSS UDMAP TX channel has been configured", "action_short": "UDMAP_TX_CH_CFG", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "NavSS Device ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "TX channel index"}]}, "0x01000": {"action_long": "NavSS UDMAP TX channel configuration", "action_short": "UDMAP_TX_CH_CFG", "sub_actions": {"0x00": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "UDMA device ID"}]}, "0x01": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "index"}]}, "0x07": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "upper 16-bits of valid_params"}]}, "0x08": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "lower 16-bits of valid_params"}]}, "0x0A": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "pause on error"}]}, "0x0B": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "atype"}]}, "0x0C": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "type"}]}, "0x0D": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "fetch size"}]}, "0x0E": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "txcq_qnum"}]}, "0x0F": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "priority"}]}, "0x10": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "QOS"}]}, "0x11": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "order ID"}]}, "0x12": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "scheduling priority"}]}, "0x1F": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "burst size"}]}, "0x20": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "filter einfo"}]}, "0x21": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "filter pswords"}]}, "0x22": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "teardown packet suppression"}]}, "0x23": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "credit count"}]}, "0x24": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "fdepth"}]}, "0x25": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "tdtype"}]}, "0x26": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "extended_ch_type"}]}}}}}, "0x0F": {"versions": {"0x00000": {"action_long": "NavSS UDMAP TX channel get configuration", "action_short": "UDMAP_TX_CH_GET_CFG", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "NavSS Device ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "TX channel index"}]}, "0x01000": {"action_long": "NavSS UDMAP TX channel get configuration", "action_short": "UDMAP_TX_CH_GET_CFG", "sub_actions": {"0x00": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "UDMA device ID"}]}, "0x01": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "index"}]}, "0x02": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "get_reset_cfg"}]}}}}}, "0x10": {"versions": {"0x00000": {"action_long": "NavSS UDMAP GCFG region has been configured", "action_short": "UDMAP_GCFG_CFG", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "NavSS Device ID"}]}, "0x01000": {"action_long": "NavSS UDMAP GCFG region has been configured", "action_short": "UDMAP_GCFG_CFG", "sub_actions": {"0x00": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "UDMA device ID"}]}, "0x07": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "upper 16-bits of valid_params"}]}, "0x08": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "lower 16-bits of valid_params"}]}}}}}, "0x11": {"action_long": "NavSS UDMAP RX channel has been allocated", "action_short": "UDMAP_RX_CH_ALLOC (LEGACY-UNSUPPORTED)", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "NavSS Device ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "RX channel index"}]}, "0x12": {"action_long": "NavSS UDMAP RX channel has been freed", "action_short": "UDMAP_RX_CH_FREE (LEGACY-UNSUPPORTED)", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "NavSS Device ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "RX channel index"}]}, "0x13": {"action_long": "NavSS UDMAP RX channel has been modified", "action_short": "UDMAP_RX_CH_INDEX", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "NavSS Device ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "RX channel index"}]}, "0x14": {"versions": {"0x00000": {"action_long": "NavSS UDMAP RX channel thread ID modified", "action_short": "UDMAP_RX_CH_THRD_ID", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 0, "name": "PSI-L thread ID programmed into the RX channel THRD_ID register"}]}, "0x01000": {"action_long": "Set NavSS UDMAP RX channel thread ID", "action_short": "UDMAP_RX_CH_SET_THRD_ID", "sub_actions": {"0x01": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "index"}]}, "0x05": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "NavSS subsystem device ID"}]}, "0x1E": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "PSI-L thread ID"}]}}}}}, "0x15": {"versions": {"0x00000": {"action_long": "NavSS UDMAP RX channel has been configured", "action_short": "UDMAP_RX_CH_CFG", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "NavSS Device ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "RX channel index"}]}, "0x01000": {"action_long": "NavSS UDMAP RX channel configuration", "action_short": "UDMAP_RX_CH_CFG", "sub_actions": {"0x00": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "UDMA device ID"}]}, "0x01": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "index"}]}, "0x07": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "upper 16-bits of valid_params"}]}, "0x08": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "lower 16-bits of valid_params"}]}, "0x0A": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "pause on error"}]}, "0x0B": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "atype"}]}, "0x0C": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "type"}]}, "0x0D": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "fetch size"}]}, "0x0E": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "rxcq_qnum"}]}, "0x0F": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "priority"}]}, "0x10": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "QOS"}]}, "0x11": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "order ID"}]}, "0x12": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "scheduling priority"}]}, "0x1F": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "burst size"}]}, "0x20": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "flow ID start"}]}, "0x21": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "flow ID count"}]}, "0x22": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "ignore short"}]}, "0x23": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "ignore long"}]}}}}}, "0x16": {"versions": {"0x00000": {"action_long": "NavSS UDMAP RX channel get configuration", "action_short": "UDMAP_RX_CH_GET_CFG", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "NavSS Device ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "RX channel index"}]}, "0x01000": {"action_long": "NavSS UDMAP RX channel get configuration", "action_short": "UDMAP_RX_CH_GET_CFG", "sub_actions": {"0x00": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "UDMA device ID"}]}, "0x01": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "index"}]}, "0x02": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "get_reset_cfg"}]}}}}}, "0x17": {"versions": {"0x00000": {"action_long": "NavSS UDMAP GCFG region get configuration", "action_short": "UDMAP_GCFG_GET_CFG", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "NavSS Device ID"}]}, "0x01000": {"action_long": "NavSS UDMAP GCFG region get configuration", "action_short": "UDMAP_GCFG_GET_CFG", "sub_actions": {"0x00": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "UDMA device ID"}]}, "0x02": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "get_reset_cfg"}]}}}}}, "0x18": {"versions": {"0x01002": {"action_long": "NavSS UDMAP RX flow has been allocated", "action_short": "UDMAP_RX_FLOW_ALLOC (LEGACY-UNSUPPORTED)", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "NavSS Device ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "RX flow index"}]}, "0x01003": {"action_long": "NavSS proxy driver init", "action_short": "PROXY_INIT"}}}, "0x19": {"versions": {"0x01002": {"action_long": "NavSS UDMAP RX flow has been freed", "action_short": "UDMAP_RX_FLOW_FREE (LEGACY-UNSUPPORTED)", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "NavSS Device ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "RX flow index"}]}, "0x01003": {"action_long": "NavSS proxy configuration", "action_short": "PROXY_CFG", "sub_actions": {"0x00": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "NavSS proxy device ID"}]}, "0x01": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "index"}]}, "0x07": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "upper 16-bits of valid_params"}]}, "0x08": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "lower 16-bits of valid_params"}]}}}}}, "0x1A": {"versions": {"0x01002": {"action_long": "NavSS UDMAP RX flow non-optional configure", "action_short": "UDMAP_RX_FLOW_CFG (LEGACY-UNSUPPORTED)", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "NavSS Device ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "RX flow index"}]}, "0x01003": {"action_long": "Set OES register in NavSS proxy", "action_short": "PROXY_OES_SET", "sub_actions": {"0x00": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "NavSS proxy device ID"}]}, "0x01": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "proxy index"}]}, "0x03": {"msg_data": [{"fmt": "0x%04x", "higher_bit": 16, "lower_bit": 0, "name": "global event"}]}, "0x04": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "OES register index"}]}}}}}, "0x1B": {"versions": {"0x01002": {"action_long": "NavSS UDMAP RX flow optional configure", "action_short": "UDMAP_RX_FLOW_OPT_CFG (LEGACY-UNSUPPORTED)", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "NavSS Device ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "RX flow index"}]}, "0x01003": {"action_long": "Get NavSS proxy OES configuration", "action_short": "PROXY_OES_GET", "sub_actions": {"0x00": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "NavSS proxy device ID"}]}, "0x01": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "proxy index"}]}, "0x03": {"msg_data": [{"fmt": "0x%04x", "higher_bit": 16, "lower_bit": 0, "name": "global event"}]}, "0x04": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "OES register index"}]}}}}}, "0x1C": {"action_long": "NavSS UDMAP RX flow RX channel owner", "action_short": "UDMAP_RX_FLOW_CH_OWNER (LEGACY-UNSUPPORTED)", "msg_data": [{"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "RX channel index owning the rx flow being configured"}]}, "0x1D": {"versions": {"0x00000": {"action_long": "NavSS UDMAP RX flow standard configure", "action_short": "UDMAP_FLOW_CFG", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "NavSS Device ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "RX flow index"}]}, "0x01000": {"action_long": "NavSS UDMAP RX flow standard configure", "action_short": "UDMAP_FLOW_CFG", "sub_actions": {"0x00": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "UDMA device ID"}]}, "0x01": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "index"}]}, "0x07": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "upper 16-bits of valid_params"}]}, "0x08": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "lower 16-bits of valid_params"}]}, "0x0A": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "rx_einfo_present"}]}, "0x0B": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "rx_psinfo_present"}]}, "0x0C": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "rx_error_handling"}]}, "0x0D": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "rx_desc_type"}]}, "0x0E": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "rx_sop_offset"}]}, "0x0F": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "rx_ps_location"}]}, "0x10": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "rx_src_tag_hi/lo_sel"}]}, "0x11": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "rx_dest_tag_hi/lo_sel"}]}, "0x12": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "rx_dest_qnum"}]}, "0x13": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "rx_fdq0_sz0_qnum"}]}, "0x14": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "rx_fdq1_qnum"}]}, "0x15": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "rx_fdq2_qnum"}]}, "0x16": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "rx_fdq3_qnum"}]}}}}}, "0x1E": {"versions": {"0x00000": {"action_long": "NavSS UDMAP RX flow size threshold cfg", "action_short": "UDMAP_FLOW_SZ_CFG", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "NavSS Device ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "RX flow index"}]}, "0x01000": {"action_long": "NavSS UDMAP RX flow size threshold cfg", "action_short": "UDMAP_FLOW_SZ_CFG", "sub_actions": {"0x00": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "UDMA device ID"}]}, "0x01": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "index"}]}, "0x07": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "upper 16-bits of valid_params"}]}, "0x08": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "lower 16-bits of valid_params"}]}, "0x17": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "rx_fdq0_sz1_qnum"}]}, "0x18": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "rx_fdq0_sz2_qnum"}]}, "0x19": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "rx_fdq0_sz3_qnum"}]}, "0x1A": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "rx_size_thresh_en"}]}}}}}, "0x1F": {"versions": {"0x00000": {"action_long": "NavSS UDMAP OES event has been programmed", "action_short": "UDMAP_OES_INDEX", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "NavSS Device ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "OES register index"}]}, "0x01000": {"action_long": "Set OES register in NavSS UDMAP channel", "action_short": "UDMAP_OES_SET", "sub_actions": {"0x00": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "UDMA device ID"}]}, "0x01": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "channel index"}]}, "0x03": {"msg_data": [{"fmt": "0x%04x", "higher_bit": 16, "lower_bit": 0, "name": "global event"}]}, "0x04": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "OES register index"}]}}}}}, "0x20": {"versions": {"0x00000": {"action_long": "NavSS UDMAP OES event has been programmed", "action_short": "UDMAP_OES_EVENT", "msg_data": [{"fmt": "%d", "higher_bit": 15, "lower_bit": 0, "name": "Global event programmed into OES register"}]}, "0x01000": {"action_long": "Get NavSS UDMAP channel OES configuration", "action_short": "UDMAP_OES_GET", "sub_actions": {"0x00": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "UDMA device ID"}]}, "0x01": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "channel index"}]}, "0x03": {"msg_data": [{"fmt": "0x%04x", "higher_bit": 16, "lower_bit": 0, "name": "global event"}]}, "0x04": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "OES register index"}]}}}}}, "0x21": {"versions": {"0x00000": {"action_long": "NavSS UDMAP RX flow get standard config", "action_short": "UDMAP_FLOW_GET_CFG", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "NavSS Device ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "RX flow index"}]}, "0x01000": {"action_long": "NavSS UDMAP RX flow get standard config", "action_short": "UDMAP_FLOW_GET_CFG", "sub_actions": {"0x00": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "UDMA device ID"}]}, "0x01": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "index"}]}, "0x02": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "get_reset_cfg"}]}}}, "0x03003": {"action_long": "UDMAP common flow delegate", "action_short": "UDMAP_FLOW_DELEGATE", "sub_actions": {"0x00": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "UDMA device ID"}]}, "0x01": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "index"}]}, "0x07": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "upper 16-bits of valid_params"}]}, "0x08": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "lower 16-bits of valid_params"}]}, "0x0A": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "flow delegation support check"}]}, "0x0B": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "delegated_host"}]}, "0x0C": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "clear"}]}}}}}, "0x22": {"versions": {"0x00000": {"msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "NavSS Device ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "RX flow index"}]}, "0x01000": {"action_long": "NavSS UDMAP RX flow get size threshold cfg", "action_short": "UDMAP_FLOW_SZ_GET_CFG", "sub_actions": {"0x00": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "UDMA device ID"}]}, "0x01": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "index"}]}, "0x02": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "get_reset_cfg"}]}}}}}, "0x23": {"versions": {"0x01000": {"action_long": "PSI-L driver init", "action_short": "PSIL_INIT"}}}, "0x24": {"versions": {"0x00000": {"action_long": "NavSS PSI-L threads have been paired", "action_short": "PSIL_PAIR", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "NavSS Device ID"}]}, "0x01000": {"action_long": "Pair NavSS PSI-L threads", "action_short": "PSIL_PAIR", "sub_actions": {"0x00": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "PSI-L proxy device ID"}]}, "0x0A": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "PSI-L source thread"}]}, "0x0B": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "PSI-L destination thread"}]}, "0x0C": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "PSIL-L thread enabled prior to pairing"}]}}}}}, "0x25": {"action_long": "NavSS PSI-L paired source thread ID", "action_short": "PSIL_PAIR_SRC_THRD_ID", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 0, "name": "PSI-L source thread ID"}]}, "0x26": {"versions": {"0x00000": {"action_long": "NavSS PSI-L paired destination thread ID", "action_short": "PSIL_PAIR_DST_THRD_ID", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 0, "name": "PSI-L destination thread ID"}]}, "0x01000": {"action_long": "Read NavSS PSI-L thread configuration register", "action_short": "PSIL_READ", "sub_actions": {"0x00": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "PSI-L proxy device ID"}]}, "0x0F": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "PSI-L thread"}]}, "0x10": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "PSI-L thread configuration register address"}]}, "0x11": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "Upper 16-bits of read PSI-L thread configuration register"}]}, "0x12": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "Lower 16-bits of read PSI-L thread configuration register"}]}}}}}, "0x27": {"versions": {"0x00000": {"action_long": "NavSS PSI-L thread real-time register read/write", "action_short": "PSIL_RD_WR_ID", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 21, "name": "Read (0)/Write (1)"}, {"fmt": "%d", "higher_bit": 15, "lower_bit": 0, "name": "PSI-L thread ID"}]}, "0x01000": {"action_long": "Write NavSS PSI-L thread configuration register", "action_short": "PSIL_WRITE", "sub_actions": {"0x00": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "PSI-L proxy device ID"}]}, "0x0F": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "PSI-L thread"}]}, "0x10": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "PSI-L thread configuration register address"}]}, "0x11": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "Upper 16-bits of data to write to PSI-L thread configuration register"}]}, "0x12": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "Lower 16-bits of data to write to PSI-L thread configuration register"}]}}}}}, "0x28": {"versions": {"0x00000": {"action_long": "NavSS PSI-L threads have been unpaired", "action_short": "PSIL_UNPAIR", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "NavSS Device ID"}]}, "0x01000": {"action_long": "Unpair NavSS PSI-L threads", "action_short": "PSIL_UNPAIR", "sub_actions": {"0x00": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "PSI-L proxy device ID"}]}, "0x0A": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "PSI-L source thread"}]}, "0x0B": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "PSI-L destination thread"}]}, "0x0D": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "PSIL-L thread disabled prior to pairing"}]}, "0x0E": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "source thread peer register thread value"}]}}}}}, "0x29": {"versions": {"0x00000": {"action_long": "NavSS PSI-L unpaired source thread ID", "action_short": "PSIL_UNPAIR_SRC_THRD_ID", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 0, "name": "PSI-L source thread ID"}]}, "0x01000": {"action_long": "DRU thread offset for UTC control programming", "action_short": "PSIL_DRU_DST_OFFSET", "sub_actions": {"0x00": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "PSI-L proxy device ID"}]}, "0x0F": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "PSI-L thread"}]}}}, "0x03007": {"action_long": "Verify message host ID matches resource host ID", "action_short": "PSIL_HOST_ID_CHECK", "sub_actions": {"0x13": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "Resource host ID in RM boardcfg"}]}, "0x14": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "Message host ID"}]}}}}}, "0x2A": {"action_long": "NavSS PSI-L unpaired destination thread ID", "action_short": "PSIL_UNPAIR_DST_THRD_ID", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 0, "name": "PSI-L destination thread ID"}]}, "0x2B": {"versions": {"0x01000": {"action_long": "IRQ driver init", "action_short": "IRQ_INIT"}}}, "0x2C": {"versions": {"0x00000": {"action_long": "Programmed interrupt route source", "action_short": "IRQ_SET_SRC", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "Interrupt Source Device ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "Device's interrupt source index"}]}, "0x01000": {"action_long": "Program interrupt route", "action_short": "IRQ_SET", "sub_actions": {"0x07": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "upper 16-bits of valid_params"}]}, "0x08": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "lower 16-bits of valid_params"}]}, "0x0A": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "Interrupt aggregator device ID"}]}, "0x0B": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "Global event"}]}, "0x0C": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "Destination host IRQ index"}]}, "0x0D": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "Secondary host"}]}, "0x0E": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "Source device ID"}]}, "0x0F": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "Source index"}]}, "0x10": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "Destination device ID"}]}, "0x11": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "Virtual interrupt"}]}, "0x12": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "Virtual interrupt status bit index"}]}}}}}, "0x2D": {"versions": {"0x00000": {"action_long": "Programmed interrupt route destination", "action_short": "IRQ_SET_DST", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "Interrupt Destination Device ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "Interrupt destination input IRQ index"}]}, "0x01000": {"action_long": "Release interrupt route", "action_short": "IRQ_RELEASE", "sub_actions": {"0x07": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "upper 16-bits of valid_params"}]}, "0x08": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "lower 16-bits of valid_params"}]}, "0x0A": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "Interrupt aggregator device ID"}]}, "0x0B": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "Global event"}]}, "0x0C": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "Destination host IRQ index"}]}, "0x0D": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "Secondary host"}]}, "0x0E": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "Source device ID"}]}, "0x0F": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "Source index"}]}, "0x10": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "Destination device ID"}]}, "0x11": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "Virtual interrupt"}]}, "0x12": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "Virtual interrupt status bit index"}]}}}}}, "0x2E": {"versions": {"0x00000": {"action_long": "Released interrupt route source", "action_short": "IRQ_REL_SRC", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "Interrupt Source Device ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "Device's interrupt source index"}]}, "0x02001": {"action_long": "Set OES register in Interrupt Aggregator", "action_short": "IRQ_IA_OES_SET", "sub_actions": {"0x00": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "IA device ID"}]}, "0x03": {"msg_data": [{"fmt": "0x%04x", "higher_bit": 16, "lower_bit": 0, "name": "global event"}]}, "0x04": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "OES register index"}]}}}}}, "0x2F": {"versions": {"0x00000": {"action_long": "Released interrupt route destination", "action_short": "IRQ_REL_DST", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "Interrupt Destination Device ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "Interrupt destination input IRQ index"}]}, "0x02001": {"action_long": "Get OES register configuration in Interrupt Aggregator", "action_short": "IRQ_IA_OES_GET", "sub_actions": {"0x00": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "IA device ID"}]}, "0x03": {"msg_data": [{"fmt": "0x%04x", "higher_bit": 16, "lower_bit": 0, "name": "global event"}]}, "0x04": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "OES register index"}]}}}}}, "0x30": {"versions": {"0x00000": {"action_long": "IA virtual interrupt programmed", "action_short": "IRQ_IA_CFG_VINT", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "Interrupt Aggregator Device ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "Virtual interrupt"}]}, "0x01000": {"action_long": "IA driver init", "action_short": "IRQ_IA_INIT"}}}, "0x31": {"versions": {"0x00000": {"action_long": "IA VINT event and status bit programmed", "action_short": "IRQ_IA_CFG_VINT_EVT", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 16, "name": "IA virtual interrupt status bit programmed"}, {"fmt": "%d", "higher_bit": 15, "lower_bit": 0, "name": "IA input event mapped to status bit"}]}, "0x01000": {"action_long": "Map an event to an IA virtual interrupt", "action_short": "IRQ_IA_MAP_VINT", "sub_actions": {"0x00": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "IA device ID"}]}, "0x0A": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "virtual interrupt"}]}, "0x0B": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "global event"}]}, "0x0C": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "Virtual interrupt status bit"}]}}}}}, "0x32": {"versions": {"0x00000": {"action_long": "IA virtual interrupt cleared", "action_short": "IRQ_IA_CLR_VINT", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "Interrupt Aggregator Device ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "Virtual interrupt"}]}, "0x01000": {"action_long": "Unmap an event from an IA virtual interrupt", "action_short": "IRQ_IA_UNMAP_VINT", "sub_actions": {"0x00": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "IA device ID"}]}, "0x0A": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "virtual interrupt"}]}, "0x0B": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "global event"}]}, "0x0C": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "Virtual interrupt status bit"}]}}}}}, "0x33": {"action_long": "IA VINT event and status bit cleared", "action_short": "IRQ_IA_CLR_VINT_EVT", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 16, "name": "IA virtual interrupt status bit cleared"}, {"fmt": "%d", "higher_bit": 15, "lower_bit": 0, "name": "IA input event unmapped from status bit"}]}, "0x34": {"versions": {"0x00000": {"action_long": "IR input to output route programmed", "action_short": "IRQ_IR_CFG", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "Interrupt Router Device ID"}]}, "0x01000": {"action_long": "IR driver init", "action_short": "IRQ_IR_INIT"}}}, "0x35": {"versions": {"0x00000": {"action_long": "Programmed IR input and output", "action_short": "IRQ_IR_CFG_IO", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 10, "name": "IR input index"}, {"fmt": "%d", "higher_bit": 9, "lower_bit": 0, "name": "IR output index"}]}, "0x01000": {"action_long": "Configured IR input to output mapping", "action_short": "IRQ_IR_CFG", "sub_actions": {"0x00": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "IR device ID"}]}, "0x0A": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "IR input index"}]}, "0x0B": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "IR output index"}]}}}}}, "0x36": {"versions": {"0x00000": {"action_long": "IR input to output route cleared", "action_short": "IRQ_IR_CLR", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "Interrupt Router Device ID"}]}, "0x01000": {"action_long": "Cleared IR input to output mapping", "action_short": "IRQ_IR_CLR", "sub_actions": {"0x00": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "IR device ID"}]}, "0x0A": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "IR input index"}]}, "0x0B": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "IR output index"}]}}}}}, "0x37": {"action_long": "Cleared IR input and output", "action_short": "IRQ_IR_CLR_IO", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 10, "name": "IR input index"}, {"fmt": "%d", "higher_bit": 9, "lower_bit": 0, "name": "IR output index"}]}, "0x3A": {"versions": {"0x01000": {"action_long": "RM board configuration resource assignment list validation", "action_short": "RESASG_VALIDATE_BRDCFG_LIST", "sub_actions": {"0x06": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "Resource assignment utype"}]}, "0x0A": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "host"}]}, "0x0B": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "resource range start"}]}, "0x0C": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "resource range number"}]}}}}}, "0x3B": {"versions": {"0x01000": {"action_long": "RM resource assignment firewall configuration", "action_short": "RESASG_FIREWALL_CFG", "sub_actions": {"0x01": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "Resource index"}]}, "0x06": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "Resource assignment utype"}]}, "0x0D": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "firewall ID"}]}, "0x0E": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "firewall channel"}]}}}}}, "0x3C": {"versions": {"0x01000": {"action_long": "RM validate resource against board configuration", "action_short": "RESASG_VALIDATE_RESOURCE", "sub_actions": {"0x01": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "Resource index"}]}, "0x06": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "Resource assignment utype"}]}, "0x0A": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "host"}]}}}}}, "0x3D": {"versions": {"0x00000": {"action_long": "Retrieved a resource range for a host", "action_short": "RESOURCE_GET", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 16, "name": "Host requesting range"}, {"fmt": "%d", "higher_bit": 15, "lower_bit": 6, "name": "Resource type"}, {"fmt": "%d", "higher_bit": 5, "lower_bit": 0, "name": "Resource sub-type"}]}, "0x01000": {"action_long": "Retrieve a resource range for a host", "action_short": "RESOURCE_GET", "sub_actions": {"0x06": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "Resource assignment utype"}]}, "0x0A": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "type"}]}, "0x0B": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "subtype"}]}, "0x0C": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "Resource range start"}]}, "0x0D": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "Resource range number"}]}, "0x0E": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "Secondary host"}]}}}, "0x03002": {"action_long": "Retrieve a resource range for a host", "action_short": "RESOURCE_GET", "sub_actions": {"0x06": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "Resource assignment utype"}]}, "0x0A": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "type"}]}, "0x0B": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "subtype"}]}, "0x0C": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "Resource range start"}]}, "0x0D": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "Resource range number"}]}, "0x0E": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "Secondary host"}]}, "0x0F": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "Second resource range start"}]}, "0x10": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "Second resource range number"}]}}}}}, "0x3E": {"versions": {"0x01001": {"action_long": "Retrieved resource range start index", "action_short": "RESOURCE_GET_START", "msg_data": [{"fmt": "%d", "higher_bit": 15, "lower_bit": 0, "name": "Resource start index"}]}, "0x01002": {"action_long": "Validating device group of resource", "action_short": "DEVGRP_VALIDATE", "sub_actions": {"0x00": {"msg_data": [{"fmt": "%d", "higher_bit": 16, "lower_bit": 0, "name": "Device ID being validated"}]}, "0x0A": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "Device group given in RM boardcfg message"}]}, "0x0B": {"msg_data": [{"fmt": "0x%04X", "higher_bit": 16, "lower_bit": 0, "name": "Device group of configured resource"}]}}}}}, "0x3F": {"action_long": "Retrieved resource range number", "action_short": "RESOURCE_GET_NUM", "msg_data": [{"fmt": "%d", "higher_bit": 15, "lower_bit": 0, "name": "Number of resources"}]}, "0x7F": {"action_long": "Generic Debug Message", "action_short": "GENERIC_DEBUG", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 0, "name": "Message Data"}]}}}, "0x03": {"action_domain_name": "Power Management", "action_modifiers": [{"mask": "0x40", "modifier_long": "Action failed", "modifier_short": "FAIL"}], "actions": {"0x00": {"action_long": "Device has been Turned ON", "action_short": "DEVICE_ON", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 0, "name": "Device ID"}]}, "0x01": {"action_long": "<PERSON><PERSON> has been Turned OFF", "action_short": "DEVICE_OFF", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 0, "name": "Device ID"}]}, "0x02": {"action_long": "Clock has been Turned ON", "action_short": "CLOCK_ENABLE", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 0, "name": "Clock ID"}]}, "0x03": {"action_long": "Clock has been Turned OFF", "action_short": "CLOCK_DISABLE", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 0, "name": "Clock ID"}]}, "0x04": {"action_long": "Clock Frequency has been changed: significand * 2^Exponent Hz", "action_short": "CLOCK_SET_RATE", "msg_data": [{"fmt": "%d", "higher_bit": 9, "lower_bit": 0, "name": "Clock ID"}, {"fmt": "%d", "higher_bit": 16, "lower_bit": 10, "name": "Clock Frequency{significand}"}, {"fmt": "%d", "higher_bit": 21, "lower_bit": 17, "name": "Clock Frequency{Exponent}"}]}, "0x05": {"action_long": "Clock parent has been changed", "action_short": "CLOCK_SET_PARENT", "msg_data": [{"fmt": "%d", "higher_bit": 9, "lower_bit": 0, "name": "Clock ID"}, {"fmt": "%d", "higher_bit": 21, "lower_bit": 10, "name": "New Parent ID"}]}, "0x06": {"action_long": "TI-SCI message received", "action_short": "MSG_RECEIVED", "msg_data": [{"fmt": "0x%08X", "higher_bit": 21, "lower_bit": 0, "name": "Message ID"}]}, "0x07": {"action_long": "TI-SCI message content: dev/clk-ids", "action_short": "MSG_PARAM_DEV_CLK_ID", "msg_data": [{"fmt": "%d", "higher_bit": 9, "lower_bit": 0, "name": "Device ID"}, {"fmt": "%d", "higher_bit": 21, "lower_bit": 10, "name": "Clock ID"}]}, "0x08": {"action_long": "TI-SCI message content: value", "action_short": "MSG_PARAM_VAL", "msg_data": [{"fmt": "0x%08X", "higher_bit": 21, "lower_bit": 0, "name": "Target Value"}]}, "0x09": {"action_long": "ARM wakeup event received", "action_short": "WAKE_ARM", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 0, "name": "Host ID"}]}, "0x0A": {"action_long": "Wakeup handler executed", "action_short": "WAKE_HANDLER", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 0, "name": "Interrupt ID"}]}, "0x0B": {"action_long": "Power Domain Get", "action_short": "PD_GET", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 20, "name": "PSC ID"}, {"fmt": "%d", "higher_bit": 19, "lower_bit": 14, "name": "Power domain ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "PD Usage Count"}]}, "0x0C": {"action_long": "Power Domain Put", "action_short": "PD_PUT", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 20, "name": "PSC ID"}, {"fmt": "%d", "higher_bit": 19, "lower_bit": 14, "name": "Power domain ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "PD Usage Count"}]}, "0x0D": {"action_long": "Set Local Reset", "action_short": "SET_LOCAL_RESET", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 20, "name": "PSC ID"}, {"fmt": "%d", "higher_bit": 19, "lower_bit": 14, "name": "LPSC ID"}, {"fmt": "%d", "higher_bit": 0, "lower_bit": 0, "name": "Enable(1)/Disable(0)"}]}, "0x0E": {"action_long": "Module Get", "action_short": "MODULE_GET", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 20, "name": "PSC ID"}, {"fmt": "%d", "higher_bit": 19, "lower_bit": 14, "name": "LPSC ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "Module Use Count"}]}, "0x0F": {"action_long": "Module Put", "action_short": "MODULE_PUT", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 20, "name": "PSC ID"}, {"fmt": "%d", "higher_bit": 19, "lower_bit": 14, "name": "LPSC ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "Module Use Count"}]}, "0x10": {"action_long": "Retention Get", "action_short": "RETENTION_GET", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 20, "name": "PSC ID"}, {"fmt": "%d", "higher_bit": 19, "lower_bit": 14, "name": "LPSC ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "Mo<PERSON>le Retention Count"}]}, "0x11": {"action_long": "Retention Put", "action_short": "RETENTION_PUT", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 20, "name": "PSC ID"}, {"fmt": "%d", "higher_bit": 19, "lower_bit": 14, "name": "LPSC ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "Mo<PERSON>le Retention Count"}]}, "0x12": {"action_long": "Initialize Power Domains", "action_short": "PD_INIT", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 20, "name": "LPSC ID"}, {"fmt": "%d", "higher_bit": 19, "lower_bit": 14, "name": "Power domain ID"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "Mo<PERSON>le Retention Count"}]}, "0x13": {"action_long": "PSC Invalid Data", "action_short": "PSC_INV_DATA", "msg_data": []}, "0x14": {"action_long": "Transition timeout", "action_short": "PD_TRANS_TIMEOUT", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 20, "name": "PSC ID"}, {"fmt": "%d", "higher_bit": 19, "lower_bit": 14, "name": "Power domain ID"}, {"fmt": "%d", "higher_bit": 2, "lower_bit": 0, "name": "Internal position"}]}, "0x15": {"action_long": "Invalid Dependency data", "action_short": "PD_INV_DEP_DATA", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 20, "name": "Dependent Power domain ID"}, {"fmt": "%d", "higher_bit": 19, "lower_bit": 14, "name": "Power domain ID"}, {"fmt": "%d", "higher_bit": 2, "lower_bit": 0, "name": "Internal position"}]}, "0x16": {"action_long": "Reset Done timeout", "action_short": "PD_RSTDNE_TIMEOUT", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 20, "name": "PSC ID"}, {"fmt": "%d", "higher_bit": 19, "lower_bit": 14, "name": "Power domain ID"}, {"fmt": "%d", "higher_bit": 2, "lower_bit": 0, "name": "Internal position"}]}, "0x17": {"action_long": "Set Module Reset", "action_short": "SET_MODULE_RESET", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 20, "name": "PSC ID"}, {"fmt": "%d", "higher_bit": 19, "lower_bit": 14, "name": "LPSC ID"}, {"fmt": "%d", "higher_bit": 0, "lower_bit": 0, "name": "Enable(1)/Disable(0)"}]}, "0x20": {"action_long": "Power Management Initialization", "action_short": "PM_INIT", "msg_data": [{"fmt": "%d", "higher_bit": 20, "lower_bit": 20, "name": "Result_error"}, {"fmt": "%d", "higher_bit": 19, "lower_bit": 19, "name": "Result_defer"}, {"fmt": "%d", "higher_bit": 18, "lower_bit": 16, "name": "startup_idx"}, {"fmt": "%d", "higher_bit": 15, "lower_bit": 0, "name": "error_code"}]}, "0x21": {"action_long": "Power Management device Initialization", "action_short": "PM_DEV_INIT", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 12, "name": "Device ID"}, {"fmt": "%d", "higher_bit": 11, "lower_bit": 0, "name": "error_code"}]}, "0x22": {"action_long": "Power Management system reset", "action_short": "PM_SYS_RESET", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 14, "name": "Domain"}, {"fmt": "%d", "higher_bit": 13, "lower_bit": 0, "name": "error_code"}]}, "0x30": {"action_long": "Low power mode sequence", "action_short": "PM_LPM_SEQ", "msg_data": [{"fmt": "%X", "higher_bit": 21, "lower_bit": 8, "name": "Sequence number", "sub_msg": {"0x01": {"msg": "DM stub started"}, "0x02": {"msg": "Unlocked MMRs"}, "0x03": {"msg": "Putting DDR into self refresh"}, "0x04": {"msg": "DDR in reset isolation"}, "0x05": {"msg": "Enabled USB reset isolation"}, "0x06": {"msg": "Disabled main LPSC"}, "0x07": {"msg": "Bypassed main PLL"}, "0x08": {"msg": "Configured wake-up sources"}, "0x09": {"msg": "Enabled Main IO isolation"}, "0x0A": {"msg": "Disabled second set of main LPSC"}, "0x0B": {"msg": "Switched from MAIN PLL to MCU PLL"}, "0x0C": {"msg": "Configured clock muxes"}, "0x0D": {"msg": "Disabled Main PLL"}, "0x0E": {"msg": "Enabled DM reset mask"}, "0x0F": {"msg": "Kept main domain in reset"}, "0x10": {"msg": "Disabled MCU domain"}, "0x11": {"msg": "Masked main domain reset"}, "0x12": {"msg": "Gated WWD clock"}, "0x13": {"msg": "Bypassed MCU PLL"}, "0x14": {"msg": "Disabled clock switch on clock loss"}, "0x15": {"msg": "Powered off HFOSC"}, "0x16": {"msg": "Enabled gating of clock on WFI"}, "0x17": {"msg": "DM in WFI"}, "0x18": {"msg": "LPM wake event"}, "0x19": {"msg": "DM exited WFI"}, "0x1A": {"msg": "Disabled gating of clock on WFI"}, "0x1B": {"msg": "Powered on HFOSC"}, "0x1C": {"msg": "Enabled clock switch on clock loss"}, "0x1D": {"msg": "Restored MCU PLL"}, "0x1E": {"msg": "unmasked WWD clock"}, "0x1F": {"msg": "Disabling MCU IO isolation"}, "0x20": {"msg": "Wrote stub magic word"}, "0x21": {"msg": "Powered on main domain"}, "0x22": {"msg": "Reset status"}, "0x51": {"msg": "Enabling main LPSC"}, "0x23": {"msg": "Moving to SMS PLL from MCU PLL"}, "0x24": {"msg": "Enabled remaining MCU PLL"}, "0x25": {"msg": "TIFS core is ready"}, "0x26": {"msg": "Loaded FS stub"}, "0x27": {"msg": "Received continue resume"}, "0x28": {"msg": "Disabled main IO isolation"}, "0x29": {"msg": "Enabled remaining PLL"}, "0x2A": {"msg": "Removed DDR reset isolation"}, "0x2B": {"msg": "Exited DDR self reset"}, "0x2C": {"msg": "Disabled USB reset isolation"}, "0x2D": {"msg": "Sent continue resume to TFS"}, "0x2E": {"msg": "Received sync resume message"}, "0x2F": {"msg": "Locked MCU CTRL MMR "}, "0x30": {"msg": "FS stub started"}, "0x31": {"msg": "Received continue resume on FS stub from DM stub"}, "0x32": {"msg": "TIFS context restored"}, "0x33": {"msg": "Removed DM reset mask"}, "0x34": {"msg": "Removed main domain reset isolation"}}}, {"fmt": "%d", "higher_bit": 7, "lower_bit": 0, "name": " Value"}]}, "0x3B": {"action_long": "API attempted to set exclusive to busy device", "action_short": "EXCLUSIVE_BUSY", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 12, "name": "Device ID"}, {"fmt": "%d", "higher_bit": 11, "lower_bit": 6, "name": "Requester Host ID"}, {"fmt": "%d", "higher_bit": 5, "lower_bit": 0, "name": "Holder Host ID"}]}, "0x3C": {"action_long": "API attempted to modify exclusive device", "action_short": "EXCLUSIVE_DEVICE", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 8, "name": "Device ID"}, {"fmt": "%d", "higher_bit": 7, "lower_bit": 0, "name": "Host ID"}]}, "0x3D": {"action_long": "API attempted to set invalid state", "action_short": "INVALID_STATE", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 0, "name": "Device ID"}]}, "0x3E": {"action_long": "API received bad device ID", "action_short": "BAD_DEVICE", "msg_data": [{"fmt": "%d", "higher_bit": 21, "lower_bit": 0, "name": "Device ID"}]}, "0x7F": {"action_long": "Generic Debug Message", "action_short": "GENERIC_DEBUG", "msg_data": [{"fmt": "0x%08X", "higher_bit": 21, "lower_bit": 0, "name": "Message Data"}]}}}}}