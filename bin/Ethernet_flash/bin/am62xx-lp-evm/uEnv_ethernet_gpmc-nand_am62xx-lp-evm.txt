# This uEnv.txt file can contain additional environment settings that you
# want to set in U-Boot at boot time.  This can be simple variables such
# as the serverip or custom variables.  The format of this file is:
#    variable=value

user_commands=echo Flashing_on_GPMC_NAND; sf probe; mtd list; run command_list;

# ************To flash tiboot3.bin************

# erase
cmd_1=nand erase.part NAND.tiboot3

# Enter your filename in place of tiboot3.bin
cmd_2=dhcp tiboot3.bin 

# write
cmd_3=nand write ${loadaddr} NAND.tiboot3 ${filesize}

# ************To flash tispl.bin************

# erase
cmd_4=nand erase.part NAND.tispl

# Enter your filename in place of tispl.bin
cmd_5=dhcp tispl.bin

# write
cmd_6=nand write ${loadaddr} NAND.tispl ${filesize}

# ************To flash u-boot.img************

# erase
cmd_7=nand erase.part NAND.u-boot

# Enter your filename in place of u-boot.img
cmd_8=dhcp u-boot.img

# write
cmd_9=nand write ${loadaddr} NAND.u-boot ${filesize}

# ************To flash uEnv.txt************

# erase
cmd_10=nand erase.part NAND.u-boot-env

# Enter your filename in place of uEnv.txt
cmd_11=dhcp uEnv.txt

# write
cmd_12=nand write ${loadaddr} NAND.u-boot-env ${filesize}

# ************To flash rootfs************

# erase
cmd_13=nand erase.part NAND.file-system

# Enter your filename in place of rootfs
cmd_14=dhcp rootfs

# write
cmd_15=nand write ${loadaddr} NAND.file-system ${filesize}

cmd_16=echo Flashing_Done

# Run the list of commands specified by variables. 
# Add or remove the commands you want to run

command_list=run cmd_1; run cmd_2; run cmd_3; run cmd_4; run cmd_5; run cmd_6; run cmd_7; run cmd_8; run cmd_9; run cmd_10; run cmd_11; run cmd_12; run cmd_13; run cmd_14; run cmd_15; run cmd_16;
