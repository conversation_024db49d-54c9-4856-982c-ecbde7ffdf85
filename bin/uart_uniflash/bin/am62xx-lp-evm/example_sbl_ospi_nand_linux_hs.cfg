#-----------------------------------------------------------------------------#
#                                                                             #
#      EXAMPLE CONFIGURATION FILE TO BE USED WITH THE FLASHWRITER SCRIPT      #
#                                                                             #
#-----------------------------------------------------------------------------#
#
# - You can customized this config file to point to your own bootloader and/or application images
# - You can use --operation=flashverify if you just want to verify the flash contents and not flash the file.
#   how to use: python uart_uniflash.py -p {name of your UART com port} --cfg={path to your edited config file}
#   help: python uart_uniflash.py --help
#

# First point to sbl_uart_uniflash_stage1 binary, which initialises DDR and receives sbl_uart_uniflash_stage2 binary
--flash-writer=<path to file>/sbl_uart_uniflash_stage1.release.hs.tiimage

# When sending sbl_uart_uniflash_stage2 binary make sure to flash at SOC memory offset 0x0.
# Points to sbl_uart_uniflash_stage2 binary, which function's as a server to flash one or more
--file=<path to file>/sbl_uart_uniflash_stage2.release.appimage.hs --operation=flash --flash-offset=0x0

# Now send one or more files to flash or flashverify as needed. The order of sending files does not matter

# Flash tiboot3.bin
--file=<path to file>/tiboot3.bin --operation=flash --flash-offset=0x0

# Flash tispl.bin
--file=<path to file>/tispl.bin --operation=flash --flash-offset=0x80000

# Flash u-boot.img
--file=<path to file>/u-boot.img --operation=flash --flash-offset=0x280000
