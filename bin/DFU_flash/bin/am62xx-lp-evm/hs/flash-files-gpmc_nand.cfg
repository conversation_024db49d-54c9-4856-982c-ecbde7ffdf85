# ┌────────────────────────────────────────────────────────────────────────────────────────┐
# │                          ===== FLASH CONFIGURATION FILE =====                          │
# │ ## Config Line Syntax Rules                                                            │
# │ - A config line is a whitespace separated collection of `--<key>=<value>` pairs.       │
# │ - Avoid using spaces other than as the pair separator.                                 │
# │ - The supported keys and their values are                                              │
# │ | Key            | Value                                                        |      │
# │ | -------------- | ------------------------------------------------------------ |      │
# │ | --file         | Path of the file to be flashed. Use quotes for Windows path. |      │
# │ | --operation    | `flash-nor`, `flash-nand`, `flash-emmc`, `flash-gpmc_nand`   |      │
# │ | --flash-offset | Raw address offset in hex at which file is to be flashed.    |      │
# │ | --attributes   | Only for `flash-emmc`. Skipped for others. Default="raw,1,-" |      │
# │ |                | Three comma separated values: "<raw|part>,<hwpart>,<partid>" |      │
# └────────────────────────────────────────────────────────────────────────────────────────┘

# R5F SPL
--file="bin\am62xx-lp-evm\hs\images\tiboot3.bin" --operation=flash-gpmc_nand --flash-offset=0x0

# A53 SPL
--file="bin\am62xx-lp-evm\hs\images\tispl.bin" --operation=flash-gpmc_nand --flash-offset=0x200000

# A53 U-Boot
--file="bin\am62xx-lp-evm\hs\images\u-boot.img" --operation=flash-gpmc_nand --flash-offset=0x600000

# # Rootfs
# --file="bin\am62xx-lp-evm\hs\images\rootfs.img" --operation=flash-gpmc_nand --flash-offset=0x0

