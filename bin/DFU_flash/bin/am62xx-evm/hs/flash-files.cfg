# ┌────────────────────────────────────────────────────────────────────────────────────────┐
# │                          ===== FLASH CONFIGURATION FILE =====                          │
# │ ## Config Line Syntax Rules                                                            │
# │ - A config line is a whitespace separated collection of `--<key>=<value>` pairs.       │
# │ - Avoid using spaces other than as the pair separator.                                 │
# │ - The supported keys and their values are                                              │
# │ | Key            | Value                                                        |      │
# │ | -------------- | ------------------------------------------------------------ |      │
# │ | --file         | Path of the file to be flashed. Use quotes for Windows path. |      │
# │ | --operation    | One of `flash-nor`, `flash-nand`, or `flash-emmc`.           |      │
# │ | --flash-offset | Raw address offset in hex at which file is to be flashed.    |      │
# │ | --attributes   | Only for `flash-emmc`. Skipped for others. Default="raw,1,-" |      │
# │ |                | Three comma separated values: "<raw|part>,<hwpart>,<partid>" |      │
# └────────────────────────────────────────────────────────────────────────────────────────┘

# R5F SPL @Raw flash to Boot0 hardware partition
--file="bin\am62xx-evm\hs\images\tiboot3.bin" --operation=flash-emmc --flash-offset=0x0 --attributes="raw,1,-"

# A53 SPL @Raw flash to Boot0 hardware partition
--file="bin\am62xx-evm\hs\images\tispl.bin" --operation=flash-emmc --flash-offset=0x80000 --attributes="raw,1,-"

# A53 U-Boot @Raw flash to Boot0 hardware partition
--file="bin\am62xx-evm\hs\images\u-boot.img" --operation=flash-emmc --flash-offset=0x280000 --attributes="raw,1,-"

# # Rootfs @Raw flash to GPT or DOS partition 1 in UDA hardware partition
# --file="bin\am62xx-evm\hs\images\rootfs.img" --operation=flash-emmc --flash-offset=0x0 --attributes="part,0,1"

