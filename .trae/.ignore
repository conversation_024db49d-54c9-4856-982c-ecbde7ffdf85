# Prerequisites
*.d

# Object files
*.o
*.ko
*.obj
*.elf

# Linker output
*.ilk
*.map
*.exp

# Precompiled Headers
*.gch
*.pch

# Libraries
*.lib
*.a
*.la
*.lo

# Shared objects (inc. Windows DLLs)
*.dll
*.so
*.so.*
*.dylib

# Executables
*.exe
*.out
*.app
*.i*86
*.x86_64
*.hex

# Debug files
*.dSYM/
*.su
*.idb
*.pdb

# Kernel Module Compile Results
*.tmp
*.mod*
*.cmd
.tmp_versions/
modules.order
Module.symvers
Mkfile.old
dkms.conf

# Custom files
example-applications/*
filesystem/*
licenses/*
busybox/*
board-support/linux-extras-6.1.83+gitAUTOINC+3c0d73833e-ti-rt/*
board-support/u-boot-build/*
board-support/u-boot-extras-jailhouse-2023.04+gitAUTOINC+68f2264747/*
board-support/prebuilt-images/*
k3r5-devkit/*
linux-devkit/*
external-toolchain-dir/*
filesystem/*
!board-support/k3-respart-tool
!board-support/prebuilt-images
rootfs/*
*.pyc
.gitignore