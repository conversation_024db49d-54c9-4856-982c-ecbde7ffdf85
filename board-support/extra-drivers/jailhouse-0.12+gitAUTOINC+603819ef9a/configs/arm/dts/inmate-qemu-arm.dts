/*
 * Jailhouse, a Linux-based partitioning hypervisor
 *
 * Device tree for Linux inmate test on QEMU ARM target,
 * corresponds to configs/arm/qemu-arm-linux-demo.c
 *
 * Copyright (c) Siemens AG, 2016-2020
 *
 * Authors: <AUTHORS>
 *
 * This work is licensed under the terms of the GNU GPL, version 2.  See
 * the COPYING file in the top-level directory.
 */

#include <dt-bindings/interrupt-controller/arm-gic.h>

/dts-v1/;

/ {
	model = "Jailhouse cell on QEMU ARM";

	#address-cells = <1>;
	#size-cells = <1>;

	interrupt-parent = <&gic>;

	hypervisor {
		compatible = "jailhouse,cell";
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu@2 {
			compatible = "arm,cortex-a15";
			device_type = "cpu";
			reg = <2>;
		};

		cpu@3 {
			compatible = "arm,cortex-a15";
			device_type = "cpu";
			reg = <3>;
		};
	};

	psci {
		compatible = "arm,psci-0.2";
		method = "smc";
	};

	timer {
		compatible = "arm,armv7-timer";
		interrupts = <GIC_PPI 13
				(GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 14
				(GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 11
				(GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 10
				(GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>;
	};

	gic: interrupt-controller@8000000 {
		compatible = "arm,cortex-a15-gic";
		#interrupt-cells = <3>;
		interrupt-controller;
		reg = <0x08000000 0x1000>, /* GICD */
		      <0x08010000 0x1000>; /* GICC */
	};

	apb_pclk: clk24mhz {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <24000000>;
		clock-output-names = "clk24mhz";
	};

	uart0: serial@9000000 {
		compatible = "arm,pl011", "arm,primecell";
		reg = <0x09000000 0x1000>;
		interrupts = <GIC_SPI 1 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&apb_pclk>, <&apb_pclk>;
		clock-names = "uartclk", "apb_pclk";
	};

	pci@7000000 {
		compatible = "pci-host-ecam-generic";
		device_type = "pci";
		bus-range = <0 0>;
		#address-cells = <3>;
		#size-cells = <2>;
		#interrupt-cells = <1>;
		interrupt-map-mask = <0 0 0 7>;
		interrupt-map = <0 0 0 1 &gic GIC_SPI 108 IRQ_TYPE_EDGE_RISING>,
				<0 0 0 2 &gic GIC_SPI 109 IRQ_TYPE_EDGE_RISING>,
				<0 0 0 3 &gic GIC_SPI 110 IRQ_TYPE_EDGE_RISING>,
				<0 0 0 4 &gic GIC_SPI 111 IRQ_TYPE_EDGE_RISING>;
		reg = <0x08e00000 0x100000>;
		ranges =
			<0x02000000 0x00 0x10000000 0x10000000 0x00 0x10000>;
	};
};
