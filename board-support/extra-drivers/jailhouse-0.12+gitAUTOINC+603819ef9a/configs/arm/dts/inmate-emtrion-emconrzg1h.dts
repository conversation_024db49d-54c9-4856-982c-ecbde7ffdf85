/*
 * Jailhouse, a Linux-based partitioning hypervisor
 *
 * Device tree for Linux inmate test on emCON-RZ/G1H board,
 * corresponds to configs/arm/emtrion-emconrzg1h-linux-demo.c
 *
 * Copyright (c) emtrion GmbH, 2017
 *
 * Authors: <AUTHORS>
 *
 * This work is licensed under the terms of the GNU GPL, version 2.  See
 * the COPYING file in the top-level directory.
 */

#include <dt-bindings/interrupt-controller/arm-gic.h>

#define R8A7742_PD_CA15_CPU3		3
#define R8A7742_PD_ALWAYS_ON		32

/dts-v1/;

/memreserve/ 0x0000000070000000 0x0000000000002000;
/ {
	model = "Jailhouse cell on emCON-RZ/G1H";
	compatible = "emtrion,emconrzg1h", "renesas,r8a7742";

	#address-cells = <2>;
	#size-cells = <2>;

	interrupt-parent = <&gic>;

	aliases {
		i2c2 = "/i2c@e6530000";
		serial5 = "/serial@e6c40000";
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu@3 {
			enable-method = "psci";
			device_type = "cpu";
			compatible = "arm,cortex-a15";
			reg = <3>;
			clock-frequency = <**********>;
			power-domains = <&sysc R8A7742_PD_CA15_CPU3>;
		};
	};

	memory@70000000 {
		device_type = "memory";
		reg = <0x0 0x70000000 0x0 0x0bef0000>;
	};

	chosen {
		bootargs = "console=ttySC5,115200 root=/dev/sda1 rootwait
			    ip=dhcp loglevel=8 vt.global_cursor_default=0
			    consoleblank=0";
		stdout-path = "/serial@e6c40000";
	};

	timer {
		compatible = "arm,armv7-timer";
		interrupts = <GIC_PPI 13
				(GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 14
				(GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 11
				(GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 10
				(GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>;
	};

	vcc_sdhi0: regulator@0 {
		compatible = "regulator-fixed";

		regulator-name = "SDHI0 Vcc";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;

		regulator-boot-on;
		regulator-always-on;

	};

	vccq_sdhi0: regulator@1 {
		compatible = "regulator-fixed";

		regulator-name = "SDHI0 VccQ";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;

		regulator-boot-on;
		regulator-always-on;
	};

	gic: interrupt-controller@f1001000 {
		compatible = "arm,cortex-a7-gic", "arm,cortex-a15-gic";
		reg = <0x0 0xf1001000 0x0 0x1000>,
		      <0x0 0xf1002000 0x0 0x1000>;
		interrupt-controller;
		#interrupt-cells = <3>;
	};

	sysc: system-controller@e6180000 {
		compatible = "renesas,r8a7742-sysc";
		reg = <0 0xe6180000 0 0x0200>;
		#power-domain-cells = <1>;
	};

	pfc: pin-controller@e6060000 {
		compatible = "renesas,pfc-r8a7742";
		reg = <0 0xe6060000 0 0x250>;

		serial5 {
			renesas,groups = "scifa0_data_b";
			renesas,function = "scifa0";
		};

		sdhi0_pins: sd0 {
			renesas,groups = "sdhi0_data4", "sdhi0_ctrl",
					 "sdhi0_cd", "sdhi0_wp";
			renesas,function = "sdhi0";
		};
	};

	clocks {
		scifa0_clk: scifa0_clk {
			compatible = "fixed-clock";
			#clock-cells = <1>;
			clock-frequency  = <52000000>;
			clock-output-names = "scifa0_clk";
		};
		sdhi0_clk: sdhi0_clk {
			compatible = "fixed-clock";
			#clock-cells = <1>;
			clock-frequency  = <195000000>;
			clock-output-names = "sdhi0_clk";
		};
		i2c2_clk: i2c2_clk {
			compatible = "fixed-clock";
			#clock-cells = <1>;
			clock-frequency  = <100000>;
			clock-output-names = "i2c2_clk";
		};
	};

	scifa0: serial@e6c40000 {
		compatible = "renesas,scifa-r8a7742", "renesas,scifa";
		reg = <0 0xe6c40000 0 0x40>;
		interrupts = <0 144 IRQ_TYPE_LEVEL_HIGH>;
		power-domains = <&sysc R8A7742_PD_ALWAYS_ON>;
		status = "okay";
		clocks = <&scifa0_clk 0>;
		clock-names = "peripheral_clk";
		pinctrl-names = "default";
	};

	sdhi0: sd@ee100000 {
		compatible = "renesas,sdhi-r8a7742";
		reg = <0 0xee100000 0 0x200>;
		interrupts = <0 165 IRQ_TYPE_LEVEL_HIGH>;
		power-domains = <&sysc R8A7742_PD_ALWAYS_ON>;
		status = "okay";
		clocks = <&sdhi0_clk 0>;
		vmmc-supply = <&vcc_sdhi0>;
		vqmmc-supply = <&vccq_sdhi0>;
	};

	i2c2: i2c@e6530000 {
		compatible = "renesas,i2c-r8a7742";
		reg = <0 0xe6530000 0 0x40>;
		interrupts = <0 286 IRQ_TYPE_LEVEL_HIGH>;
		power-domains = <&sysc R8A7742_PD_ALWAYS_ON>;
		clocks = <&i2c2_clk 0>;
		#address-cells = <1>;
		#size-cells = <0>;
		status = "okay";
	};
};
