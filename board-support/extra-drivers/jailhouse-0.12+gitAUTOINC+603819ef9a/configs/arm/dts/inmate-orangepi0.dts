/*
 * Jailhouse, a Linux-based partitioning hypervisor
 *
 * Device tree for Linux inmate test on Orange Pi Zero board,
 * corresponds to configs/arm/orangepi0-linux-demo.c
 *
 * Copyright (c) Siemens AG, 2016-2017
 *
 * Authors: <AUTHORS>
 *
 * This work is licensed under the terms of the GNU GPL, version 2.  See
 * the COPYING file in the top-level directory.
 */

#include <dt-bindings/interrupt-controller/arm-gic.h>

/dts-v1/;

/ {
	model = "Jailhouse cell on Orange Pi Zero";

	#address-cells = <1>;
	#size-cells = <1>;

	interrupt-parent = <&gic>;

	hypervisor {
		compatible = "jailhouse,cell";
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu@2 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			reg = <2>;
		};

		cpu@3 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			reg = <3>;
		};
	};

	psci {
		compatible = "arm,psci-0.2";
		method = "smc";
	};

	timer {
		compatible = "arm,armv7-timer";
		interrupts = <GIC_PPI 13
				(GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 14
				(GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 11
				(GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 10
				(GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>;
	};

	clocks {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		osc24M: clk24M {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <24000000>;
			clock-output-names = "osc24M";
		};
	};

	gic: interrupt-controller@01c81000 {
		compatible = "arm,cortex-a7-gic", "arm,cortex-a15-gic";
		reg = <0x01c81000 0x1000>,
		      <0x01c82000 0x1000>;
		interrupt-controller;
		#interrupt-cells = <3>;
	};

	uart: serial@01c28000 {
		compatible = "snps,dw-apb-uart";
		reg = <0x01c28000 0x400>;
		interrupts = <GIC_SPI 0 IRQ_TYPE_LEVEL_HIGH>;
		reg-shift = <2>;
		reg-io-width = <4>;
		clock-frequency = <24000000>;
	};

	pci@2000000 {
		compatible = "pci-host-ecam-generic";
		device_type = "pci";
		bus-range = <0 0>;
		#address-cells = <3>;
		#size-cells = <2>;
		#interrupt-cells = <1>;
		interrupt-map-mask = <0 0 0 7>;
		interrupt-map = <0 0 0 1 &gic GIC_SPI 123 IRQ_TYPE_EDGE_RISING>,
				<0 0 0 2 &gic GIC_SPI 124 IRQ_TYPE_EDGE_RISING>,
				<0 0 0 3 &gic GIC_SPI 125 IRQ_TYPE_EDGE_RISING>,
				<0 0 0 4 &gic GIC_SPI 126 IRQ_TYPE_EDGE_RISING>;
		reg = <0x2000000 0x100000>;
		ranges =
			<0x02000000 0x00 0x10000000 0x10000000 0x00 0x10000>;
	};
};
