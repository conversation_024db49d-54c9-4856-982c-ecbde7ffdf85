/dts-v1/;

#include "inmate-k3-am654-idk.dts"

/ {
	sdhci0: mmc@4f80000 {
		compatible = "ti,am654-sdhci-5.1";
		reg = <0x0 0x4f80000 0x0 0x260>, <0x0 0x4f90000 0x0 0x134>;
		power-domains = <&k3_pds 47 1>;
		clocks = <&k3_clks 47 0>, <&k3_clks 47 1>;
		clock-names = "clk_ahb", "clk_xin";
		interrupts = <GIC_SPI 136 IRQ_TYPE_LEVEL_HIGH>;
		mmc-ddr-1_8v;
		mmc-hs200-1_8v;
		ti,otap-del-sel-legacy = <0x0>;
		ti,otap-del-sel-mmc-hs = <0x0>;
		ti,otap-del-sel-sd-hs = <0x0>;
		ti,otap-del-sel-sdr12 = <0x0>;
		ti,otap-del-sel-sdr25 = <0x0>;
		ti,otap-del-sel-sdr50 = <0x8>;
		ti,otap-del-sel-sdr104 = <0x7>;
		ti,otap-del-sel-ddr50 = <0x5>;
		ti,otap-del-sel-ddr52 = <0x5>;
		ti,otap-del-sel-hs200 = <0x5>;
		ti,otap-del-sel-hs400 = <0x0>;
		ti,trm-icp = <0x8>;
		dma-coherent;
	};
};

&mcu_uart0 {
	power-domains = <&k3_pds 149 1>;
};

&k3_pds {
	#power-domain-cells = <2>;
};

&sdhci0 {
	/* eMMC */
	non-removable;
	ti,driver-strength-ohm = <50>;
	bus-width = <8>;
	disable-wp;
};
