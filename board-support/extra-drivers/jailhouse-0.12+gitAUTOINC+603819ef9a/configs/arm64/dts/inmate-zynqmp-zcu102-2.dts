/*
 * Jailhouse, a Linux-based partitioning hypervisor
 *
 * Device tree for 2nd Linux inmate test on ZynqMP ZCU102 board,
 * corresponds to configs/arm64/zynqmp-zcu102-linux-demo-2.c
 *
 * Copyright (c) Siemens AG, 2016
 *
 * Authors: <AUTHORS>
 *
 * This work is licensed under the terms of the GNU GPL, version 2.  See
 * the COPYING file in the top-level directory.
 */

#include <dt-bindings/interrupt-controller/arm-gic.h>

/dts-v1/;

/ {
	model = "Jailhouse cell on ZynqMP ZCU102";

	#address-cells = <2>;
	#size-cells = <2>;

	interrupt-parent = <&gic>;

	hypervisor {
		compatible = "jailhouse,cell";
	};

	cpus {
		#address-cells = <2>;
		#size-cells = <0>;

		cpu@1 {
			compatible = "arm,cortex-a53", "arm,armv8";
			device_type = "cpu";
			reg = <0x0 0x1>;
			enable-method = "psci";
		};
	};

	psci {
		compatible = "arm,psci-0.2";
		method = "smc";
	};

	timer {
		compatible = "arm,armv8-timer";
		interrupts = <GIC_PPI 13
				(GIC_CPU_MASK_SIMPLE(8) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 14
				(GIC_CPU_MASK_SIMPLE(8) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 11
				(GIC_CPU_MASK_SIMPLE(8) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 10
				(GIC_CPU_MASK_SIMPLE(8) | IRQ_TYPE_LEVEL_LOW)>;
	};

	gic: interrupt-controller@f6801000 {
		compatible = "arm,gic-400";
		reg = <0x0 0xf9010000 0x0 0x1000>,
		      <0x0 0xf902f000 0x0 0x2000>;
		interrupt-controller;
		#interrupt-cells = <3>;
	};

	pci@fc000000 {
		compatible = "pci-host-ecam-generic";
		device_type = "pci";
		bus-range = <0 0>;
		#address-cells = <3>;
		#size-cells = <2>;
		#interrupt-cells = <1>;
		interrupt-map-mask = <0 0 0 7>;
		interrupt-map = <0 0 0 1 &gic GIC_SPI 112 IRQ_TYPE_EDGE_RISING>,
				<0 0 0 2 &gic GIC_SPI 113 IRQ_TYPE_EDGE_RISING>,
				<0 0 0 3 &gic GIC_SPI 114 IRQ_TYPE_EDGE_RISING>,
				<0 0 0 4 &gic GIC_SPI 115 IRQ_TYPE_EDGE_RISING>;
		reg = <0x0 0xfc000000 0x0 0x100000>;
		ranges =
			<0x02000000 0x00 0x10000000 0x0 0x10000000 0x00 0x10000>;
	};
};
