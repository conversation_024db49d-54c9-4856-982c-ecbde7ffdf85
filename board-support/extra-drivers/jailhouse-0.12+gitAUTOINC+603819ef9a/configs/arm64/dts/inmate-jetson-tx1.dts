/*
 * Jailhouse, a Linux-based partitioning hypervisor
 *
 * Device tree for Linux inmate test on Jetson TX1 board,
 * corresponds to configs/arm64/jetson-tx1-linux-demo.c
 *
 * Copyright (c) OTH Regensburg, 2017
 *
 * Authors: <AUTHORS>
 *
 * This work is licensed under the terms of the GNU GPL, version 2.  See
 * the COPYING file in the top-level directory.
 */

#include <dt-bindings/interrupt-controller/arm-gic.h>

/dts-v1/;

/ {
	model = "Jailhouse cell on NVIDIA Jetson TX1";

	#address-cells = <1>;
	#size-cells = <1>;

	interrupt-parent = <&gic>;

	hypervisor {
		compatible = "jailhouse,cell";
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu@2 {
			compatible = "arm,cortex-a57";
			device_type = "cpu";
			enable-method = "psci";
			reg = <0x2>;
		};

		cpu@3 {
			compatible = "arm,cortex-a57";
			device_type = "cpu";
			enable-method = "psci";
			reg = <0x3>;
		};
	};

	psci {
		compatible = "arm,psci-0.2";
		method = "smc";
	};

	gic: interrupt-controller@50041000 {
		compatible = "arm,gic-400";
		#interrupt-cells = <3>;
		interrupt-controller;
		reg = <0x50041000 0x1000>,
		      <0x50042000 0x1000>;
	};

	timer {
		compatible = "arm,armv8-timer";
		interrupts = <GIC_PPI 13
				(GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 14
				(GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 11
				(GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 10
				(GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>;
		interrupt-parent = <&gic>;
	};

	serial@70006000 {
		compatible = "nvidia,tegra210-uart", "nvidia,tegra20-uart";
		reg = <0x70006000 0x40>;
		reg-shift = <2>;
		interrupts = <GIC_SPI 36 IRQ_TYPE_LEVEL_HIGH>;
		clock-frequency = <408000000>;
		status = "okay";
	};

	pci@48000000 {
		status = "okay";
		compatible = "pci-host-ecam-generic";
		device_type = "pci";
		bus-range = <0 0>;
		#address-cells = <3>;
		#size-cells = <2>;
		#interrupt-cells = <1>;
		interrupt-map-mask = <0 0 0 7>;
		interrupt-map = <0 0 0 1 &gic GIC_SPI 152 IRQ_TYPE_EDGE_RISING>,
				<0 0 0 2 &gic GIC_SPI 153 IRQ_TYPE_EDGE_RISING>,
				<0 0 0 3 &gic GIC_SPI 154 IRQ_TYPE_EDGE_RISING>,
				<0 0 0 4 &gic GIC_SPI 155 IRQ_TYPE_EDGE_RISING>;
		reg = <0x48000000 0x100000>;
		ranges =
			<0x02000000 0x00 0x10000000 0x10000000 0x00 0x10000>;
	};
};
