// SPDX-License-Identifier: GPL-2.0
/*
 * Device Tree Source for J7200 Jailhouse inmate kernel
 *
 * Copyright (C) 2016-2019 Texas Instruments Incorporated - http://www.ti.com/
 */

/dts-v1/;

#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <dt-bindings/input/input.h>
#include <dt-bindings/gpio/gpio.h>

#define TI_SCI_PD_EXCLUSIVE	1

/ {
	model = "Texas Instruments J7200 Inmate Model";
	compatible = "ti,j7200-evm", "ti,j7200";
	interrupt-parent = <&gic500>;
	#address-cells = <2>;
	#size-cells = <2>;

	aliases {
		serial3 = &main_uart1;
	};

	chosen {
		stdout-path = "serial3:115200n8";
	};

	hypervisor {
		compatible = "jailhouse,cell";
	};

	memory@8a0000000 {
		device_type = "memory";
		reg = <0x8 0xa0000000 0x0 0x60000000>;
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;
		cpu-map {
			cluster0: cluster0 {
				core1 {
					cpu = <&cpu1>;
				};
			};
		};

		cpu1: cpu@1 {
			compatible = "arm,cortex-a72";
			reg = <0x001>;
			device_type = "cpu";
			enable-method = "psci";
			i-cache-size = <0xC000>;
			i-cache-line-size = <64>;
			i-cache-sets = <256>;
			d-cache-size = <0x8000>;
			d-cache-line-size = <64>;
			d-cache-sets = <128>;
			next-level-cache = <&L2_0>;
		};
	};

	L2_0: l2-cache0 {
		compatible = "cache";
		cache-level = <2>;
		cache-size = <0x100000>;
		cache-line-size = <64>;
		cache-sets = <2048>;
		next-level-cache = <&msmc_l3>;
	};

	msmc_l3: l3-cache0 {
		compatible = "cache";
		cache-level = <3>;
	};

	a72_timer0: timer-cl0-cpu0 {
		compatible = "arm,armv8-timer";
		interrupts = <GIC_PPI 13 IRQ_TYPE_LEVEL_LOW>, /* cntpsirq */
			     <GIC_PPI 14 IRQ_TYPE_LEVEL_LOW>, /* cntpnsirq */
			     <GIC_PPI 11 IRQ_TYPE_LEVEL_LOW>, /* cntvirq */
			     <GIC_PPI 10 IRQ_TYPE_LEVEL_LOW>; /* cnthpirq */
	};

	pmu: pmu {
		compatible = "arm,armv8-pmuv3";
		/* Recommendation from GIC500 TRM Table A.3 */
		interrupts = <GIC_PPI 7 IRQ_TYPE_LEVEL_HIGH>;
	};

	firmware {
		psci: psci {
			compatible = "arm,psci-1.0";
			method = "smc";
		};
	};

	pci@76000000 {
		compatible = "pci-host-ecam-generic";
		device_type = "pci";
		bus-range = <0 0>;
		#address-cells = <3>;
		#size-cells = <2>;
		#interrupt-cells = <1>;
		interrupt-map-mask = <0 0 0 7>;
		interrupt-map =
		<0 0 0 1 &gic500 0 0 GIC_SPI 163 IRQ_TYPE_EDGE_RISING>,
		<0 0 0 2 &gic500 0 0 GIC_SPI 164 IRQ_TYPE_EDGE_RISING>,
		<0 0 0 3 &gic500 0 0 GIC_SPI 165 IRQ_TYPE_EDGE_RISING>,
		<0 0 0 4 &gic500 0 0 GIC_SPI 166 IRQ_TYPE_EDGE_RISING>;
		reg = <0x0 0x76000000 0x0 0x100000>;
		ranges =
		<0x02000000 0x00 0x10000000 0x0 0x10000000 0x00 0x10000>;
	};

	cbass_main: bus@100000 {
		compatible = "simple-bus";
		#address-cells = <2>;
		#size-cells = <2>;
		ranges = <0x00 0x00100000 0x00 0x00100000 0x00 0x00020000>, /* ctrl mmr */
			 <0x00 0x00600000 0x00 0x00600000 0x00 0x00031100>, /* GPIO */
			 <0x00 0x00900000 0x00 0x00900000 0x00 0x00012000>, /* serdes */
			 <0x00 0x00A40000 0x00 0x00A40000 0x00 0x00000800>, /* timesync router */
			 <0x00 0x06000000 0x00 0x06000000 0x00 0x00400000>, /* USBSS0 */
			 <0x00 0x06400000 0x00 0x06400000 0x00 0x00400000>, /* USBSS1 */
			 <0x00 0x01000000 0x00 0x01000000 0x00 0x0d000000>, /* Most peripherals */
			 <0x00 0x30000000 0x00 0x30000000 0x00 0x0c400000>, /* MAIN NAVSS */
			 <0x00 0x0d000000 0x00 0x0d000000 0x00 0x01000000>, /* PCIe Core*/
			 <0x00 0x10000000 0x00 0x10000000 0x00 0x10000000>, /* PCIe DAT */
			 <0x00 0x64800000 0x00 0x64800000 0x00 0x00800000>, /* C71 */
			 <0x4d 0x80800000 0x4d 0x80800000 0x00 0x00800000>, /* C66_0 */
			 <0x4d 0x81800000 0x4d 0x81800000 0x00 0x00800000>, /* C66_1 */
			 <0x4e 0x20000000 0x4e 0x20000000 0x00 0x00080000>, /* GPU */
			 <0x00 0x70000000 0x00 0x70000000 0x00 0x00800000>, /* MSMC RAM */

			 /* MCUSS_WKUP Range */
			 <0x00 0x28380000 0x00 0x28380000 0x00 0x03880000>,
			 <0x00 0x40200000 0x00 0x40200000 0x00 0x00998400>,
			 <0x00 0x40f00000 0x00 0x40f00000 0x00 0x00020000>,
			 <0x00 0x41000000 0x00 0x41000000 0x00 0x00020000>,
			 <0x00 0x41400000 0x00 0x41400000 0x00 0x00020000>,
			 <0x00 0x41c00000 0x00 0x41c00000 0x00 0x00100000>,
			 <0x00 0x42040000 0x00 0x42040000 0x00 0x03ac2400>,
			 <0x00 0x45100000 0x00 0x45100000 0x00 0x00c24000>,
			 <0x00 0x46000000 0x00 0x46000000 0x00 0x00200000>,
			 <0x00 0x47000000 0x00 0x47000000 0x00 0x00068400>,
			 <0x00 0x50000000 0x00 0x50000000 0x00 0x10000000>,
			 <0x05 0x00000000 0x05 0x00000000 0x01 0x00000000>,
			 <0x07 0x00000000 0x07 0x00000000 0x01 0x00000000>;

		cbass_mcu_wakeup: interconnect@28380000 {
			compatible = "simple-bus";
			#address-cells = <2>;
			#size-cells = <2>;
			ranges = <0x00 0x28380000 0x00 0x28380000 0x00 0x03880000>, /* MCU NAVSS*/
				 <0x00 0x40200000 0x00 0x40200000 0x00 0x00998400>, /* First peripheral window */
				 <0x00 0x40f00000 0x00 0x40f00000 0x00 0x00020000>, /* CTRL_MMR0 */
				 <0x00 0x41000000 0x00 0x41000000 0x00 0x00020000>, /* MCU R5F Core0 */
				 <0x00 0x41400000 0x00 0x41400000 0x00 0x00020000>, /* MCU R5F Core1 */
				 <0x00 0x41c00000 0x00 0x41c00000 0x00 0x00100000>, /* MCU SRAM */
				 <0x00 0x42040000 0x00 0x42040000 0x00 0x03ac2400>, /* WKUP peripheral window */
				 <0x00 0x45100000 0x00 0x45100000 0x00 0x00c24000>, /* MMRs, remaining NAVSS */
				 <0x00 0x46000000 0x00 0x46000000 0x00 0x00200000>, /* CPSW */
				 <0x00 0x47000000 0x00 0x47000000 0x00 0x00068400>, /* OSPI register space */
				 <0x00 0x50000000 0x00 0x50000000 0x00 0x10000000>, /* FSS OSPI0/1 data region 0 */
				 <0x05 0x00000000 0x05 0x00000000 0x01 0x00000000>, /* FSS OSPI0 data region 3 */
				 <0x07 0x00000000 0x07 0x00000000 0x01 0x00000000>; /* FSS OSPI1 data region 3*/
		};
	};
};

&cbass_main {

	gic500: interrupt-controller@1800000 {
		compatible = "arm,gic-v3";
		#address-cells = <2>;
		#size-cells = <2>;
		ranges;
		#interrupt-cells = <3>;
		interrupt-controller;
		reg = <0x00 0x01800000 0x00 0x10000>,	/* GICD */
		      <0x00 0x01900000 0x00 0x100000>;	/* GICR */

		/* vcpumntirq: virtual CPU interface maintenance interrupt */
		interrupts = <GIC_PPI 9 IRQ_TYPE_LEVEL_HIGH>;
	};

	main_gpio_intr: interrupt-controller0 {
		compatible = "ti,sci-intr";
		ti,intr-trigger-type = <1>;
		interrupt-controller;
		interrupt-parent = <&gic500>;
		#interrupt-cells = <1>;
		ti,sci = <&dmsc>;
		ti,sci-dev-id = <131>;
		ti,interrupt-ranges = <8 392 56>;
	};

	cbass_main_navss: interconnect0 {
		compatible = "simple-mfd";
		#address-cells = <2>;
		#size-cells = <2>;
		ranges = <0x00 0x30000000 0x00 0x30000000 0x00 0x0c400000>;
		ti,sci-dev-id = <199>;

		main_navss_intr: interrupt-controller1 {
			compatible = "ti,sci-intr";
			ti,intr-trigger-type = <4>;
			interrupt-controller;
			interrupt-parent = <&gic500>;
			#interrupt-cells = <1>;
			ti,sci = <&dmsc>;
			ti,sci-dev-id = <213>;
			ti,interrupt-ranges = <0 64 64>,
					      <64 448 64>,
					      <128 672 64>;
		};

		main_udmass_inta: interrupt-controller@33d00000 {
			compatible = "ti,sci-inta";
			reg = <0x0 0x33d00000 0x0 0x100000>;
			interrupt-controller;
			interrupt-parent = <&main_navss_intr>;
			msi-controller;
			ti,sci = <&dmsc>;
			ti,sci-dev-id = <209>;
			ti,interrupt-ranges = <0 0 256>;
		};

		secure_proxy_main: mailbox@32c00000 {
			compatible = "ti,am654-secure-proxy";
			#mbox-cells = <1>;
			reg-names = "target_data", "rt", "scfg";
			reg = <0x00 0x32c00000 0x00 0x100000>,
			      <0x00 0x32400000 0x00 0x100000>,
			      <0x00 0x32800000 0x00 0x100000>;
			interrupt-names = "rx_016";
			interrupts = <GIC_SPI 39 IRQ_TYPE_LEVEL_HIGH>;
		};
	};

	main_pmx0: pinmux@11c000 {
		compatible = "pinctrl-single";
		/* Proxy 0 addressing */
		reg = <0x0 0x11c000 0x0 0x2b4>;
		#pinctrl-cells = <1>;
		pinctrl-single,register-width = <32>;
		pinctrl-single,function-mask = <0xffffffff>;
	};

	main_uart1: serial@2810000 {
		compatible = "ti,j721e-uart", "ti,am654-uart";
		reg = <0x00 0x02810000 0x00 0x100>;
		reg-shift = <2>;
		reg-io-width = <4>;
		interrupts = <GIC_SPI 193 IRQ_TYPE_LEVEL_HIGH>;
		clock-frequency = <********>;
		current-speed = <115200>;
		power-domains = <&k3_pds 278 TI_SCI_PD_EXCLUSIVE>;
		clocks = <&k3_clks 278 2>;
		clock-names = "fclk";
	};

	main_gpio2: gpio@610000 {
		compatible = "ti,j721e-gpio", "ti,keystone-gpio";
		reg = <0x0 0x00610000 0x0 0x100>;
		gpio-controller;
		#gpio-cells = <2>;
		interrupt-parent = <&main_gpio_intr>;
		interrupts = <154>, <155>, <156>, <157>, <158>;
		interrupt-controller;
		#interrupt-cells = <2>;
		ti,ngpio = <69>;
		ti,davinci-gpio-unbanked = <0>;
		power-domains = <&k3_pds 107 TI_SCI_PD_EXCLUSIVE>;
		clocks = <&k3_clks 107 0>;
		clock-names = "gpio";
	};

	main_sdhci0: sdhci@4f8000 {
		compatible = "ti,j7200-sdhci-8bit", "ti,j721e-sdhci-8bit";
		reg = <0x0 0x04f80000 0x0 0x260>, <0x0 0x4f88000 0x0 0x134>;
		interrupts = <GIC_SPI 3 IRQ_TYPE_LEVEL_HIGH>;
		power-domains = <&k3_pds 91 TI_SCI_PD_EXCLUSIVE>;
		clock-names = "clk_xin", "clk_ahb";
		clocks = <&k3_clks 91 3>, <&k3_clks 91 0>;
		ti,otap-del-sel-legacy = <0x0>;
		ti,otap-del-sel-mmc-hs = <0x0>;
		ti,otap-del-sel-ddr52 = <0x6>;
		ti,otap-del-sel-hs200 = <0x8>;
		ti,otap-del-sel-hs400 = <0x0>;
		ti,strobe-sel = <0x77>;
		ti,trm-icp = <0x8>;
		bus-width = <8>;
		mmc-hs400-1_8v;
		mmc-ddr-1_8v;
		dma-coherent;
	};
};

&cbass_mcu_wakeup {
	dmsc: dmsc@******** {
		compatible = "ti,k2g-sci";
		ti,host-id = <13>;

		mbox-names = "rx", "tx";

		mboxes= <&secure_proxy_main 16>,
			<&secure_proxy_main 18>;

		reg-names = "debug_messages";
		reg = <0x00 0x******** 0x0 0x1000>;

		k3_pds: power-controller {
			compatible = "ti,sci-pm-domain";
			#power-domain-cells = <2>;
		};

		k3_clks: clocks {
			compatible = "ti,k2g-sci-clk";
			#clock-cells = <2>;
		};

		k3_reset: reset-controller {
			compatible = "ti,sci-reset";
			#reset-cells = <2>;
		};
	};

	wkup_pmx0: pinmux@4301c000 {
		compatible = "pinctrl-single";
		/* Proxy 0 addressing */
		reg = <0x00 0x4301c000 0x00 0x178>;
		#pinctrl-cells = <1>;
		pinctrl-single,register-width = <32>;
		pinctrl-single,function-mask = <0xffffffff>;
	};
};

/* Board specific device tree entries */

&main_sdhci0 {
	/* eMMC */
	non-removable;
	ti,driver-strength-ohm = <50>;
	disable-wp;
};
