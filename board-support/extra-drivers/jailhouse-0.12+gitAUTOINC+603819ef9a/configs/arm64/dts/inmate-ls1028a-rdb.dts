// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Device Tree for inmate cell on NXP LS1028ARDB platform
 *
 * Copyright 2021 NXP
 *
 * <PERSON><PERSON>-<PERSON> <<EMAIL>>
 */

/dts-v1/;

#include <dt-bindings/interrupt-controller/arm-gic.h>

/ {
	compatible = "fsl,ls1028a-rdb", "fsl,ls1028a";
	model = "LS1028A RDB Board";
	interrupt-parent = <&gic>;
	#address-cells = <2>;
	#size-cells = <2>;

	aliases {
		serial0 = &duart1;
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu1: cpu@1 {
			device_type = "cpu";
			compatible = "arm,cortex-a72";
			reg = <0x1>;
			clocks = <&clockgen 1 0>;
			next-level-cache = <&l2>;
			cpu-idle-states = <&CPU_PW20>;
			#cooling-cells = <2>;
			enable-method = "psci";
		};

		l2: l2-cache {
			compatible = "cache";
		};
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	idle-states {
		entry-method = "psci";

		CPU_PW20: cpu-pw20 {
			compatible = "arm,idle-state";
			idle-state-name = "PW20";
			arm,psci-suspend-param = <0x0>;
			entry-latency-us = <2000>;
			exit-latency-us = <2000>;
			min-residency-us = <6000>;
		};
	};

	sysclk: sysclk {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <100000000>;
		clock-output-names = "sysclk";
	};

	timer {
		compatible = "arm,armv8-timer";
		interrupts = <GIC_PPI 13 (GIC_CPU_MASK_SIMPLE(2) |
					  IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 14 (GIC_CPU_MASK_SIMPLE(2) |
					  IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 11 (GIC_CPU_MASK_SIMPLE(2) |
					  IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 10 (GIC_CPU_MASK_SIMPLE(2) |
					  IRQ_TYPE_LEVEL_LOW)>;
	};

	gic: interrupt-controller@6000000 {
		compatible = "arm,gic-v3";
		#interrupt-cells = <3>;
		interrupt-controller;
		reg = <0x0 0x6000000 0 0x10000>,
		      <0x0 0x6040000 0 0x40000>;
		interrupts = <GIC_PPI 9 (GIC_CPU_MASK_RAW(0xf) |
					 IRQ_TYPE_LEVEL_LOW)>;
	};

	soc: soc {
		compatible = "simple-bus";
		#address-cells = <2>;
		#size-cells = <2>;
		ranges;

		ddr: memory-controller@1080000 {
			compatible = "fsl,qoriq-memory-controller";
			reg = <0x0 0x1080000 0x0 0x1000>;
			interrupts = <GIC_SPI 144 IRQ_TYPE_LEVEL_HIGH>;
			big-endian;
		};

		clockgen: clock-controller@1300000 {
			compatible = "fsl,ls1028a-clockgen";
			reg = <0x0 0x1300000 0x0 0xa0000>;
			#clock-cells = <2>;
			clocks = <&sysclk>;
		};

		duart1: serial@21c0600 {
			compatible = "fsl,ns16550", "ns16550a";
			reg = <0x00 0x21c0600 0x0 0x100>;
			clocks = <&clockgen 4 1>;
			status = "okay";
		};
	};

	pci@fb500000 {
		compatible = "pci-host-ecam-generic";
		device_type = "pci";
		bus-range = <0 0>;
		#address-cells = <3>;
		#size-cells = <2>;
		#interrupt-cells = <1>;
		interrupt-map-mask = <0 0 0 7>;
		interrupt-map = <0 0 0 1 &gic GIC_SPI 8 IRQ_TYPE_EDGE_RISING>,
			<0 0 0 2 &gic GIC_SPI 9 IRQ_TYPE_EDGE_RISING>,
			<0 0 0 3 &gic GIC_SPI 10 IRQ_TYPE_EDGE_RISING>,
			<0 0 0 4 &gic GIC_SPI 11 IRQ_TYPE_EDGE_RISING>;
		reg = <0x0 0xfb500000 0x0 0x100000>;
		ranges = <0x02000000 0x00 0x10000000 0x0 0x10000000 0x00 0x10000>;
	};

	psci {
		compatible = "arm,psci-1.0", "arm,psci-0.2";
		method = "smc";
	};
};
