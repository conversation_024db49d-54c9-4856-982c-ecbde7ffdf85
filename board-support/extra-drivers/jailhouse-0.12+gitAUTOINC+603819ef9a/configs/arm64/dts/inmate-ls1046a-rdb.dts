// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Device Tree for inmate cell on NXP ls1046a RDB platform
 *
 * Copyright 2020 NXP
 *
 * Jiafei Pan <<EMAIL>>
 */

/dts-v1/;

#include <dt-bindings/interrupt-controller/arm-gic.h>

/ {
	compatible = "fsl,ls1046a-rdb", "fsl,ls1046a";
	model = "LS1046A RDB Board";
	interrupt-parent = <&gic>;
	#address-cells = <2>;
	#size-cells = <2>;

	aliases {
		serial0 = &duart1;
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu2: cpu@2 {
			device_type = "cpu";
			compatible = "arm,cortex-a72";
			reg = <0x2>;
			clocks = <&clockgen 1 0>;
			next-level-cache = <&l2>;
			cpu-idle-states = <&CPU_PH20>;
			#cooling-cells = <2>;
			enable-method = "psci";
		};

		cpu3: cpu@3 {
			device_type = "cpu";
			compatible = "arm,cortex-a72";
			reg = <0x3>;
			clocks = <&clockgen 1 0>;
			next-level-cache = <&l2>;
			cpu-idle-states = <&CPU_PH20>;
			#cooling-cells = <2>;
			enable-method = "psci";
		};

		l2: l2-cache {
			compatible = "cache";
		};
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	idle-states {
		entry-method = "psci";

		CPU_PH20: cpu-ph20 {
			compatible = "arm,idle-state";
			idle-state-name = "PH20";
			arm,psci-suspend-param = <0x0>;
			entry-latency-us = <1000>;
			exit-latency-us = <1000>;
			min-residency-us = <3000>;
		};
	};

	sysclk: sysclk {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <100000000>;
		clock-output-names = "sysclk";
	};

	reboot {
		compatible ="syscon-reboot";
		regmap = <&dcfg>;
		offset = <0xb0>;
		mask = <0x02>;
	};

	timer {
		compatible = "arm,armv8-timer";
		interrupts = <GIC_PPI 13 (GIC_CPU_MASK_RAW(0xf) |
					  IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 14 (GIC_CPU_MASK_RAW(0xf) |
					  IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 11 (GIC_CPU_MASK_RAW(0xf) |
					  IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 10 (GIC_CPU_MASK_RAW(0xf) |
					  IRQ_TYPE_LEVEL_LOW)>;
	};

	gic: interrupt-controller@1410000 {
		compatible = "arm,gic-400";
		#interrupt-cells = <3>;
		interrupt-controller;
		reg = <0x0 0x1410000 0 0x10000>, /* GICD */
		      <0x0 0x142f000 0 0x1000>, /* GICC */
		      <0x0 0x1440000 0 0x20000>, /* GICH */
		      <0x0 0x146f000 0 0x1000>; /* GICV */
		interrupts = <GIC_PPI 9 (GIC_CPU_MASK_RAW(0xf) |
					 IRQ_TYPE_LEVEL_LOW)>;
	};

	soc: soc {
		compatible = "simple-bus";
		#address-cells = <2>;
		#size-cells = <2>;
		ranges;
		dma-ranges = <0x0 0x0 0x0 0x0 0x10000 0x00000000>;
		dma-coherent;


		ddr: memory-controller@1080000 {
			compatible = "fsl,qoriq-memory-controller";
			reg = <0x0 0x1080000 0x0 0x1000>;
			interrupts = <GIC_SPI 144 IRQ_TYPE_LEVEL_HIGH>;
			big-endian;
		};

		scfg: scfg@1570000 {
			compatible = "fsl,ls1046a-scfg", "syscon";
			reg = <0x0 0x1570000 0x0 0x10000>;
			big-endian;
		};

		dcfg: dcfg@1ee0000 {
			compatible = "fsl,ls1046a-dcfg", "syscon";
			reg = <0x0 0x1ee0000 0x0 0x1000>;
			big-endian;
		};

		clockgen: clocking@1ee1000 {
			compatible = "fsl,ls1046a-clockgen";
			reg = <0x0 0x1ee1000 0x0 0x1000>;
			#clock-cells = <2>;
			clocks = <&sysclk>;
		};

		duart1: serial@21c0600 {
			compatible = "fsl,ns16550", "ns16550a";
			reg = <0x00 0x21c0600 0x0 0x100>;
			clocks = <&clockgen 4 1>;
			status = "okay";
		};

	};

	pci@fb500000 {
		compatible = "pci-host-ecam-generic";
		device_type = "pci";
		bus-range = <0 0>;
		#address-cells = <3>;
		#size-cells = <2>;
		#interrupt-cells = <1>;
		interrupt-map-mask = <0 0 0 7>;
		interrupt-map = <0 0 0 1 &gic GIC_SPI 28 IRQ_TYPE_EDGE_RISING>,
			<0 0 0 2 &gic GIC_SPI 29 IRQ_TYPE_EDGE_RISING>,
			<0 0 0 3 &gic GIC_SPI 30 IRQ_TYPE_EDGE_RISING>,
			<0 0 0 4 &gic GIC_SPI 31 IRQ_TYPE_EDGE_RISING>;
		reg = <0x0 0xfb500000 0x0 0x100000>;
		ranges = <0x02000000 0x00 0x10000000 0x0 0x10000000 0x00 0x10000>;
	};

	psci {
		compatible = "arm,psci-1.0";
		method = "smc";
	};

};
