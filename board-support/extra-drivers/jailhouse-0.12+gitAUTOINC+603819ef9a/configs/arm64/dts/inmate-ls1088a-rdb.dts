// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Device Tree for inmate cell on NXP LS1088ARDB platform
 *
 * Copyright 2021 NXP
 *
 * <PERSON><PERSON>-<PERSON> <<EMAIL>>
 */

/dts-v1/;

#include <dt-bindings/interrupt-controller/arm-gic.h>

/ {
	compatible = "fsl,ls1088a";
	model = "LS1088A RDB Board";
	interrupt-parent = <&gic>;
	#address-cells = <2>;
	#size-cells = <2>;

	aliases {
		serial0 = &duart1;
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu2: cpu@2 {
			device_type = "cpu";
			compatible = "arm,cortex-a53";
			reg = <0x2>;
			clocks = <&clockgen 1 0>;
			cpu-idle-states = <&CPU_PH20>;
			#cooling-cells = <2>;
			enable-method = "psci";
		};

		cpu3: cpu@3 {
			device_type = "cpu";
			compatible = "arm,cortex-a53";
			reg = <0x3>;
			clocks = <&clockgen 1 0>;
			cpu-idle-states = <&CPU_PH20>;
			#cooling-cells = <2>;
			enable-method = "psci";
		};
	};

	idle-states {
		entry-method = "psci";

		 CPU_PH20: cpu-ph20 {
			compatible = "arm,idle-state";
			idle-state-name = "PH20";
			arm,psci-suspend-param = <0x0>;
			entry-latency-us = <1000>;
			exit-latency-us = <1000>;
			min-residency-us = <3000>;
		};
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	sysclk: sysclk {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <100000000>;
		clock-output-names = "sysclk";
	};

	timer {
		compatible = "arm,armv8-timer";
		interrupts = <1 13 IRQ_TYPE_LEVEL_LOW>,
			     <1 14 IRQ_TYPE_LEVEL_LOW>,
			     <1 11 IRQ_TYPE_LEVEL_LOW>,
			     <1 10 IRQ_TYPE_LEVEL_LOW>;
	};

	gic: interrupt-controller@6000000 {
		compatible = "arm,gic-v3";
		#interrupt-cells = <3>;
		interrupt-controller;
		reg = <0x0 0x06000000 0 0x10000>, /* GIC Dist */
			<0x0 0x06100000 0 0x100000>, /* GICR(RD_base+SGI_base)*/
			<0x0 0x0c0c0000 0 0x2000>, /* GICC */
			<0x0 0x0c0d0000 0 0x1000>, /* GICH */
			<0x0 0x0c0e0000 0 0x20000>; /* GICV */
		interrupts = <1 9 IRQ_TYPE_LEVEL_HIGH>;
	};

	soc: soc {
		compatible = "simple-bus";
		#address-cells = <2>;
		#size-cells = <2>;
		ranges;

		clockgen: clocking@1300000 {
			compatible = "fsl,ls1088a-clockgen";
			reg = <0x0 0x1300000 0x0 0xa0000>;
			#clock-cells = <2>;
			clocks = <&sysclk>;
		};

		duart1: serial@21c0600 {
			compatible = "fsl,ns16550", "ns16550a";
			reg = <0x00 0x21c0600 0x0 0x100>;
			clocks = <&clockgen 4 1>;
			status = "okay";
		};
	};

	pci@13000000 {
		compatible = "pci-host-ecam-generic";
		device_type = "pci";
		bus-range = <0 0>;
		#address-cells = <3>;
		#size-cells = <2>;
		#interrupt-cells = <1>;
		interrupt-map-mask = <0 0 0 7>;
		interrupt-map = <0 0 0 1 &gic GIC_SPI 49 IRQ_TYPE_EDGE_RISING>,
			<0 0 0 2 &gic GIC_SPI 50 IRQ_TYPE_EDGE_RISING>,
			<0 0 0 3 &gic GIC_SPI 51 IRQ_TYPE_EDGE_RISING>,
			<0 0 0 4 &gic GIC_SPI 56 IRQ_TYPE_EDGE_RISING>;
		reg = <0x0 0x13000000 0x0 0x100000>;
		ranges = <0x02000000 0x00 0x10000000 0x0 0x10000000 0x00 0x10000>;
	};

	psci {
		compatible = "arm,psci-0.2";
		method = "smc";
	};
};
