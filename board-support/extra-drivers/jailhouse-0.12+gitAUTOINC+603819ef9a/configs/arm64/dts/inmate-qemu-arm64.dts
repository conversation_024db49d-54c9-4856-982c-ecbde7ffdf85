/*
 * Jailhouse, a Linux-based partitioning hypervisor
 *
 * Device tree for Linux inmate test on QEMU ARM64 target,
 * corresponds to configs/arm64/qemu-arm64-linux-demo.c
 *
 * Copyright (c) Siemens AG, 2016-2017
 *
 * Authors: <AUTHORS>
 *
 * This work is licensed under the terms of the GNU GPL, version 2.  See
 * the COPYING file in the top-level directory.
 */

#include <dt-bindings/interrupt-controller/arm-gic.h>

/dts-v1/;

/ {
	model = "Jailhouse cell on QEMU ARM64";

	#address-cells = <2>;
	#size-cells = <2>;

	interrupt-parent = <&gic>;

	hypervisor {
		compatible = "jailhouse,cell";
	};

	cpus {
		#address-cells = <2>;
		#size-cells = <0>;

		cpu@2 {
			compatible = "arm,cortex-a57", "arm,armv8";
			device_type = "cpu";
			reg = <0x0 0x2>;
			enable-method = "psci";
		};

		cpu@3 {
			compatible = "arm,cortex-a57", "arm,armv8";
			device_type = "cpu";
			reg = <0x0 0x3>;
			enable-method = "psci";
		};
	};

	psci {
		compatible = "arm,psci-0.2";
		method = "smc";
	};

	timer {
		compatible = "arm,armv8-timer";
		interrupts = <GIC_PPI 13 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_PPI 14 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_PPI 11 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_PPI 10 IRQ_TYPE_LEVEL_HIGH>;
	};

	gic: interrupt-controller@d1d00000 {
		compatible = "arm,gic-v3";
		#interrupt-cells = <3>;
		interrupt-controller;
		reg = <0x0 0x08000000 0x0 0x10000>, /* GICD */
		      <0x0 0x080a0000 0x0 0xf60000>; /* GICR */
	};

	apb_pclk: clk24mhz {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <24000000>;
		clock-output-names = "clk24mhz";
	};

	uart0: serial@9000000 {
		compatible = "arm,pl011", "arm,primecell";
		reg = <0x0 0x09000000 0x0 0x1000>;
		interrupts = <GIC_SPI 1 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&apb_pclk>, <&apb_pclk>;
		clock-names = "uartclk", "apb_pclk";
	};

	pci@7000000 {
		compatible = "pci-host-ecam-generic";
		device_type = "pci";
		bus-range = <0 0>;
		#address-cells = <3>;
		#size-cells = <2>;
		#interrupt-cells = <1>;
		interrupt-map-mask = <0 0 0 7>;
		interrupt-map = <0 0 0 1 &gic GIC_SPI 108 IRQ_TYPE_EDGE_RISING>,
				<0 0 0 2 &gic GIC_SPI 109 IRQ_TYPE_EDGE_RISING>,
				<0 0 0 3 &gic GIC_SPI 110 IRQ_TYPE_EDGE_RISING>,
				<0 0 0 4 &gic GIC_SPI 111 IRQ_TYPE_EDGE_RISING>;
		reg = <0x0 0x08e00000 0x0 0x100000>;
		ranges =
			<0x02000000 0x00 0x10000000 0x0 0x10000000 0x00 0x10000>;
	};
};
