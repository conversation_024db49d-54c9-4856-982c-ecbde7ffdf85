/*
 * Jailhouse, a Linux-based partitioning hypervisor
 *
 * Device tree for Linux inmate test on Banana Pi board,
 * corresponds to configs/arm/bananapi-linux-demo.c
 *
 * Copyright (c) Siemens AG, 2016
 *
 * Authors: <AUTHORS>
 *
 * This work is licensed under the terms of the GNU GPL, version 2.  See
 * the COPYING file in the top-level directory.
 */

#include <dt-bindings/interrupt-controller/arm-gic.h>

/dts-v1/;

/ {
	model = "Jailhouse cell on HiKey";

	#address-cells = <2>;
	#size-cells = <2>;

	interrupt-parent = <&gic>;

	hypervisor {
		compatible = "jailhouse,cell";
	};

	cpus {
		#address-cells = <2>;
		#size-cells = <0>;

		cpu@102 {
			compatible = "arm,cortex-a53", "arm,armv8";
			device_type = "cpu";
			reg = <0x0 0x102>;
			enable-method = "psci";
			next-level-cache = <&CLUSTER1_L2>;
		};

		cpu@103 {
			compatible = "arm,cortex-a53", "arm,armv8";
			device_type = "cpu";
			reg = <0x0 0x103>;
			enable-method = "psci";
			next-level-cache = <&CLUSTER1_L2>;
		};

		CLUSTER1_L2: l2-cache1 {
			compatible = "cache";
		};
	};

	psci {
		compatible = "arm,psci-0.2";
		method = "smc";
	};

	timer {
		compatible = "arm,armv8-timer";
		interrupts = <GIC_PPI 13
				(GIC_CPU_MASK_SIMPLE(8) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 14
				(GIC_CPU_MASK_SIMPLE(8) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 11
				(GIC_CPU_MASK_SIMPLE(8) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 10
				(GIC_CPU_MASK_SIMPLE(8) | IRQ_TYPE_LEVEL_LOW)>;
	};

	gic: interrupt-controller@f6801000 {
		compatible = "arm,gic-400";
		reg = <0x0 0xf6801000 0x0 0x1000>,
		      <0x0 0xf6802000 0x0 0x2000>;
		interrupt-controller;
		#interrupt-cells = <3>;
	};

	uartclk: clock {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <19200000>;
	};

	uart: serial@f7113000 {
		compatible = "arm,pl011", "arm,primecell";
		reg = <0x0 0xf7113000 0x0 0x1000>;
		interrupts = <GIC_SPI 39 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&uartclk>, <&uartclk>;
		clock-names = "uartclk", "apb_pclk";
	};

	pci@f6000000 {
		compatible = "pci-host-ecam-generic";
		device_type = "pci";
		bus-range = <0 0>;
		#address-cells = <3>;
		#size-cells = <2>;
		#interrupt-cells = <1>;
		interrupt-map-mask = <0 0 0 7>;
		interrupt-map = <0 0 0 1 &gic GIC_SPI 111 IRQ_TYPE_EDGE_RISING>,
				<0 0 0 2 &gic GIC_SPI 112 IRQ_TYPE_EDGE_RISING>,
				<0 0 0 3 &gic GIC_SPI 113 IRQ_TYPE_EDGE_RISING>,
				<0 0 0 4 &gic GIC_SPI 114 IRQ_TYPE_EDGE_RISING>;
		reg = <0x0 0xf6000000 0x0 0x100000>;
		ranges =
			<0x02000000 0x00 0x10000000 0x0 0x10000000 0x00 0x10000>;
	};
};
