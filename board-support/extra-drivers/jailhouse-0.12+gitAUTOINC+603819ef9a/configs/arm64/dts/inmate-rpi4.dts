/*
 * Jailhouse, a Linux-based partitioning hypervisor
 *
 * Device tree for Linux inmate test on Raspberry Pi 4,
 * corresponds to configs/arm64/rpi4-linux-demo.c
 *
 * Copyright (c) Siemens AG, 2020
 *
 * Authors: <AUTHORS>
 *
 * This work is licensed under the terms of the GNU GPL, version 2.  See
 * the COPYING file in the top-level directory.
 */

#include <dt-bindings/interrupt-controller/arm-gic.h>

/dts-v1/;

/ {
	model = "Jailhouse cell on Raspberry Pi 4";

	#address-cells = <2>;
	#size-cells = <2>;

	interrupt-parent = <&gic>;

	hypervisor {
		compatible = "jailhouse,cell";
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu2: cpu@2 {
			device_type = "cpu";
			compatible = "arm,cortex-a72";
			reg = <2>;
			enable-method = "psci";
		};

		cpu3: cpu@3 {
			device_type = "cpu";
			compatible = "arm,cortex-a72";
			reg = <3>;
			enable-method = "psci";
		};
	};

	psci {
		compatible = "arm,psci-0.2";
		method = "smc";
	};

	timer {
		compatible = "arm,armv8-timer";
		interrupts = <GIC_PPI 13 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_PPI 14 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_PPI 11 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_PPI 10 IRQ_TYPE_LEVEL_HIGH>;
	};

	gic: interrupt-controller@ff841000 {
		compatible = "arm,gic-400";
		reg = <0x0 0xff841000 0x0 0x1000>,
		      <0x0 0xff842000 0x0 0x2000>;
		interrupt-controller;
		#interrupt-cells = <3>;
	};

	fixed: clk500mhz {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <500000000>;
		clock-output-names = "clk500mhz";
	};

	uart1: serial@fe215040 {
		compatible = "brcm,bcm2835-aux-uart";
		reg = <0x0 0xfe215040 0x0 0x40>;
		interrupts = <GIC_SPI 93 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&fixed>;
		status = "okay";
	};

	pci@e0000000 {
		compatible = "pci-host-ecam-generic";
		device_type = "pci";
		bus-range = <0 0>;
		#address-cells = <3>;
		#size-cells = <2>;
		#interrupt-cells = <1>;
		interrupt-map-mask = <0 0 0 7>;
		interrupt-map = <0 0 0 1 &gic GIC_SPI 153 IRQ_TYPE_EDGE_RISING>,
				<0 0 0 2 &gic GIC_SPI 154 IRQ_TYPE_EDGE_RISING>,
				<0 0 0 3 &gic GIC_SPI 155 IRQ_TYPE_EDGE_RISING>,
				<0 0 0 4 &gic GIC_SPI 156 IRQ_TYPE_EDGE_RISING>;
		reg = <0x0 0xff900000 0x0 0x100000>;
		ranges =
			<0x02000000 0x00 0x20000000 0x0 0x20000000 0x00 0x10000>;
	};
};
