/dts-v1/;
/ {
	fragment {
		target-path = "/";
		__overlay__ {
			pci@0 {
				compatible = "pci-host-ecam-generic";
				/*
				 * added dynamically:
				 * - device_type = "pci"
				 * - linux,pci-domain = <...>
				 * - reg = <...>
				 * - ranges = <...>
				 * - interrupt-map = <...>
				 */
				bus-range = <0 0>;
				#address-cells = <3>;
				#size-cells = <2>;
				#interrupt-cells = <1>;
				interrupt-map-mask = <0 0 0 7>;
				dma-coherent;
				/* set to "ok" during dynamic update */
				status = "disabled";
			};
		};
	};
};
