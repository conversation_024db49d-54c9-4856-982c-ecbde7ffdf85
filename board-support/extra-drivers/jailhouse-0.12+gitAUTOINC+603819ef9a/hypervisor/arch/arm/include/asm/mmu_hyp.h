/*
 * Jailhouse, a Linux-based partitioning hypervisor
 *
 * Copyright (c) ARM Limited, 2014
 *
 * Authors: <AUTHORS>
 *
 * This work is licensed under the terms of the GNU GPL, version 2.  See
 * the COPYING file in the top-level directory.
 */

#include <jailhouse/percpu.h>

int switch_exception_level(struct per_cpu *cpu_data);

void __attribute__((noreturn)) arch_shutdown_mmu(struct per_cpu *cpu_data);
