From 8a884f55bd1527baa82fab68c186ba546273860c Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON> <<EMAIL>>
Date: Sun, 6 Apr 2014 19:51:39 -0400
Subject: [PATCH] Disable installing header file provided by another package

Signed-off-by: <PERSON><PERSON> <<EMAIL>>

Upstream-Status: Inappropriate [ OE specific ]
---
 Makefile | 1 -
 1 file changed, 1 deletion(-)

diff --git a/Makefile b/Makefile
index 5a080e0..bf02396 100644
--- a/Makefile
+++ b/Makefile
@@ -33,7 +33,6 @@ install: modules_install
 
 modules_install:
 	$(MAKE) $(KERNEL_MAKE_OPTS) modules_install
-	install -m 644 -D crypto/cryptodev.h $(DESTDIR)/$(includedir)/crypto/cryptodev.h
 
 clean:
 	$(MAKE) $(KERNEL_MAKE_OPTS) clean
