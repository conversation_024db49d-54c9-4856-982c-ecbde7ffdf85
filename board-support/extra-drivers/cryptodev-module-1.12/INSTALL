=== Installation instructions ===

Simply run:
$ make
# make install

The first command compiles the code and generates the kernel module
and the latter installs the header files and the kernel module.

After that you should set your system to load the kernel module on system
load. In most systems this can be done as:
# echo "cryptodev" >>/etc/modules

or in systemd-enabled systems:
# echo "cryptodev" > /etc/modules-load.d/cryptodev.conf

=== Testing installation ===

* cryptodev-linux:
Check whether cryptodev-linux is operating as expected using the following
command.
$ make check

* OpenSSL:
run the following commands prior and after installation and compare.
$ openssl speed -evp aes-128-cbc
$ openssl speed -evp sha1

* GnuTLS 3.x:
run the following command prior and after installation and compare.
$ gnutls-cli --benchmark-ciphers

