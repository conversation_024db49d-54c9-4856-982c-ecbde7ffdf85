This is a /dev/crypto device driver, equivalent to those in OpenBSD or
FreeBSD. The main idea is to access of existing ciphers in kernel space 
from userspace, thus enabling the re-use of a hardware implementation of a
cipher.

For questions and suggestions please use the mailing lists at:
http://cryptodev-linux.org/lists.html


=== How to combine with cryptographic libraries ===

* GnuTLS: 

GnuTLS needs to be compiled with --enable-cryptodev in order to take
advantage of /dev/crypto. GnuTLS 3.0.14 or later is recommended.

* OpenSSL:

Note that OpenSSL's cryptodev implementation is outdated, and there
are issues with it. For that we recommend to use the patches
below, that we have provided to the openssl project.

http://rt.openssl.org/Ticket/Display.html?id=2770&user=guest&pass=guest

After applying the patches you can add cryptodev support by using the
-DHAVE_CRYPTODEV and -DUSE_CRYPTODEV_DIGESTS flags during compilation.
Note that the latter flag (digests) may induce a performance penalty
in some systems. 


=== Modifying and viewing verbosity at runtime ===

For debugging often the verbosity of the driver needs to be adjusted.
The sysctl tool can be used for that.

# sysctl ioctl.cryptodev_verbosity
ioctl.cryptodev_verbosity = 0

# sysctl ioctl.cryptodev_verbosity=3
ioctl.cryptodev_verbosity = 3
