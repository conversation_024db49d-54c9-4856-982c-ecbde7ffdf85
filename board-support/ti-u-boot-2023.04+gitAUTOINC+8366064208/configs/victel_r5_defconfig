CONFIG_ARM=y
CONFIG_ARCH_K3=y
CONFIG_TI_SECURE_DEVICE=y
CONFIG_SPL_STACK_R_MALLOC_SIMPLE_LEN=0x200000
CONFIG_SYS_MALLOC_F_LEN=0x9000
CONFIG_SPL_LIBCOMMON_SUPPORT=y
CONFIG_SPL_LIBGENERIC_SUPPORT=y
CONFIG_NR_DRAM_BANKS=2
CONFIG_SOC_K3_AM625=y
CONFIG_TARGET_AM625_R5_EVM=y
CONFIG_HAS_CUSTOM_SYS_INIT_SP_ADDR=y
CONFIG_CUSTOM_SYS_INIT_SP_ADDR=0x43c3a7f0
CONFIG_ENV_SIZE=0x20000
# CONFIG_DM_GPIO is not set
CONFIG_SPL_DM_SPI=y
CONFIG_DEFAULT_DEVICE_TREE="victel-am62-r5"
CONFIG_SPL_TEXT_BASE=0x43c00000
CONFIG_DM_RESET=y
# CONFIG_SPL_MMC is not set
CONFIG_SPL_SERIAL=y
# CONFIG_SPL_DRIVERS_MISC is not set
CONFIG_SPL_STACK_R_ADDR=0x82000000
CONFIG_SPL_SYS_MALLOC_F_LEN=0x7000
CONFIG_SPL_SIZE_LIMIT=0x3A7F0
CONFIG_SPL_SIZE_LIMIT_PROVIDE_STACK=0x3500
# CONFIG_SPL_FS_FAT is not set
# CONFIG_SPL_LIBDISK_SUPPORT is not set
CONFIG_SPL_SPI_FLASH_SUPPORT=y
CONFIG_SPL_SPI=y
CONFIG_SPL_LOAD_FIT=y
CONFIG_SPL_LOAD_FIT_ADDRESS=0x80080000
CONFIG_SPL_FIT_IMAGE_POST_PROCESS=y
# CONFIG_DISPLAY_CPUINFO is not set
CONFIG_SPL_SIZE_LIMIT_SUBTRACT_GD=y
CONFIG_SPL_SIZE_LIMIT_SUBTRACT_MALLOC=y
CONFIG_SPL_MAX_SIZE=0x3B000
CONFIG_SPL_PAD_TO=0x0
CONFIG_SPL_HAS_BSS_LINKER_SECTION=y
CONFIG_SPL_BSS_START_ADDR=0x43c3b000
CONFIG_SPL_BSS_MAX_SIZE=0x3000
CONFIG_SPL_SYS_REPORT_STACK_F_USAGE=y
CONFIG_SPL_SYS_MALLOC_SIMPLE=y
CONFIG_SPL_STACK_R=y
CONFIG_SPL_SEPARATE_BSS=y
CONFIG_SYS_SPL_MALLOC=y
CONFIG_HAS_CUSTOM_SPL_MALLOC_START=y
CONFIG_CUSTOM_SYS_SPL_MALLOC_ADDR=0x84000000
CONFIG_SYS_SPL_MALLOC_SIZE=0x1000000
CONFIG_SPL_EARLY_BSS=y
# CONFIG_SYS_MMCSD_RAW_MODE_U_BOOT_USE_SECTOR is not set 
# CONFIG_SYS_MMCSD_RAW_MODE_U_BOOT_SECTOR is not set
CONFIG_SPL_DM_MAILBOX=y
CONFIG_SPL_DM_SPI_FLASH=y
CONFIG_SPL_DM_RESET=y
CONFIG_SPL_POWER_DOMAIN=y
CONFIG_SPL_RAM_SUPPORT=y
CONFIG_SPL_RAM_DEVICE=y
CONFIG_SPL_REMOTEPROC=y
# CONFIG_SPL_SPI_FLASH_TINY is not set
CONFIG_SPL_SPI_FLASH_SFDP_SUPPORT=y
CONFIG_SPL_SPI_LOAD=y
CONFIG_SYS_SPI_U_BOOT_OFFS=0x80000
CONFIG_SPL_YMODEM_SUPPORT=y
# CONFIG_HUSH_PARSER is not set
# CONFIG_CMD_ASKENV is not set
# CONFIG_CMD_DFU is not set
# CONFIG_CMD_GPT is not set
# CONFIG_CMD_MMC is not set
# CONFIG_CMD_REMOTEPROC is not set
# CONFIG_CMD_SETEXPR is not set
# CONFIG_CMD_TIME is not set
# CONFIG_CMD_FAT is not set
CONFIG_OF_CONTROL=y
CONFIG_SPL_OF_CONTROL=y
CONFIG_SPL_MULTI_DTB_FIT=y
CONFIG_SPL_MULTI_DTB_FIT_NO_COMPRESSION=y
CONFIG_SYS_RELOC_GD_ENV_ADDR=y
CONFIG_SPL_DM=y
CONFIG_SPL_DM_SEQ_ALIAS=y
CONFIG_REGMAP=y
CONFIG_SPL_REGMAP=y
CONFIG_SPL_OF_TRANSLATE=y
CONFIG_CLK=y
CONFIG_SPL_CLK=y
CONFIG_SPL_CLK_CCF=y
CONFIG_SPL_CLK_K3_PLL=y
CONFIG_SPL_CLK_K3=y
CONFIG_TI_SCI_PROTOCOL=y
# CONFIG_DA8XX_GPIO is not set
CONFIG_DM_MAILBOX=y
CONFIG_K3_SEC_PROXY=y
CONFIG_SPL_MISC=y
# CONFIG_ESM_K3 is not set
# CONFIG_MMC_SDHCI is not set
# CONFIG_MMC_SDHCI_ADMA is not set
# CONFIG_SPL_MMC_SDHCI_ADMA is not set
# CONFIG_MMC_SDHCI_AM654 is not set
CONFIG_DM_SPI_FLASH=y
CONFIG_SF_DEFAULT_MODE=0
CONFIG_SF_DEFAULT_SPEED=25000000
CONFIG_SPI_FLASH_SFDP_SUPPORT=y
CONFIG_SPI_FLASH_SOFT_RESET=y
CONFIG_SPI_FLASH_SOFT_RESET_ON_BOOT=y
# CONFIG_SPI_FLASH_SPANSION is not set
# CONFIG_SPI_FLASH_S28HX_T is not set
# CONFIG_SPI_FLASH_MACRONIX=y
CONFIG_SPI_FLASH_GIGADEVICE=y
CONFIG_SPI_FLASH_ISSI=y
CONFIG_SPI_FLASH_WINBOND=y
# CONFIG_PINCTRL is not set
# CONFIG_PINCTRL_GENERIC is not set
# CONFIG_SPL_PINCTRL is not set
# CONFIG_SPL_PINCTRL_GENERIC is not set
# CONFIG_PINCTRL_SINGLE is not set
CONFIG_POWER_DOMAIN=y
CONFIG_TI_POWER_DOMAIN=y
CONFIG_K3_SYSTEM_CONTROLLER=y
CONFIG_REMOTEPROC_TI_K3_ARM64=y
CONFIG_RESET_TI_SCI=y
CONFIG_SPECIFY_CONSOLE_INDEX=y
CONFIG_DM_SERIAL=y
CONFIG_SOC_DEVICE=y
CONFIG_SOC_DEVICE_TI_K3=y
CONFIG_SOC_TI=y
CONFIG_SPI=y
CONFIG_DM_SPI=y
CONFIG_CADENCE_QSPI=y
CONFIG_TIMER=y
CONFIG_SPL_TIMER=y
CONFIG_OMAP_TIMER=y
CONFIG_LIB_RATIONAL=y
CONFIG_SPL_LIB_RATIONAL=y
# CONFIG_TI_I2C_BOARD_DETECT is not set
CONFIG_BAUDRATE=1500000
CONFIG_BOOTDELAY=0
# CONFIG_ETH is not set
# CONFIG_SPI_FLASH_UNLOCK_ALL is not set
CONFIG_LOCALVERSION_AUTO=y
CONFIG_LOCALVERSION="_V1.02.005_09020110"